import 'package:flutter_test/flutter_test.dart';
import 'package:luxury_app/shared/widgets/video_utils.dart';

void main() {
  group('VideoUtils Tests', () {
    test('should detect YouTube URLs correctly', () {
      // YouTube URLs
      expect(
        VideoUtils.isYouTubeUrl('https://www.youtube.com/watch?v=dQw4w9WgXcQ'),
        true,
      );
      expect(VideoUtils.isYouTubeUrl('https://youtu.be/dQw4w9WgXcQ'), true);
      expect(
        VideoUtils.isYouTubeUrl('https://youtube.com/embed/dQw4w9WgXcQ'),
        true,
      );
      expect(
        VideoUtils.isYouTubeUrl('https://youtube.com/v/dQw4w9WgXcQ'),
        true,
      );

      // Non-YouTube URLs
      expect(VideoUtils.isYouTubeUrl('https://example.com/video.mp4'), false);
      expect(VideoUtils.isYouTubeUrl('https://vimeo.com/123456'), false);
    });

    test('should extract YouTube video ID correctly', () {
      expect(
        VideoUtils.extractYouTubeVideoId(
          'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        ),
        'dQw4w9WgXcQ',
      );
      expect(
        VideoUtils.extractYouTubeVideoId('https://youtu.be/dQw4w9WgXcQ'),
        'dQw4w9WgXcQ',
      );
      expect(
        VideoUtils.extractYouTubeVideoId(
          'https://youtube.com/embed/dQw4w9WgXcQ',
        ),
        'dQw4w9WgXcQ',
      );
      expect(
        VideoUtils.extractYouTubeVideoId('https://youtube.com/v/dQw4w9WgXcQ'),
        'dQw4w9WgXcQ',
      );

      expect(
        VideoUtils.extractYouTubeVideoId('https://example.com/video.mp4'),
        null,
      );
    });

    test('should detect video files correctly', () {
      expect(VideoUtils.isVideoFile('https://example.com/video.mp4'), true);
      expect(VideoUtils.isVideoFile('https://example.com/video.avi'), true);
      expect(VideoUtils.isVideoFile('https://example.com/video.mov'), true);
      expect(VideoUtils.isVideoFile('https://example.com/video.webm'), true);

      expect(VideoUtils.isVideoFile('https://example.com/image.jpg'), false);
      expect(VideoUtils.isVideoFile('https://example.com/document.pdf'), false);
    });

    test('should determine video type correctly', () {
      expect(
        VideoUtils.getVideoType('https://www.youtube.com/watch?v=dQw4w9WgXcQ'),
        VideoType.youtube,
      );
      expect(
        VideoUtils.getVideoType('https://example.com/video.mp4'),
        VideoType.file,
      );
      expect(
        VideoUtils.getVideoType('https://example.com/image.jpg'),
        VideoType.unknown,
      );
    });
  });
}
