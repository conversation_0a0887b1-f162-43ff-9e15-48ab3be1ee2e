import 'package:flutter_test/flutter_test.dart';
import 'package:luxury_app/shared/widgets/video_utils.dart';

void main() {
  group('Markdown Media Tests', () {
    group('VideoUtils', () {
      test('should detect YouTube URLs correctly', () {
        expect(
          VideoUtils.isYouTubeUrl(
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          ),
          true,
        );
        expect(VideoUtils.isYouTubeUrl('https://youtu.be/dQw4w9WgXcQ'), true);
        expect(
          VideoUtils.isYouTubeUrl('https://youtube.com/embed/dQw4w9WgXcQ'),
          true,
        );
        expect(VideoUtils.isYouTubeUrl('https://example.com/video.mp4'), false);
        expect(VideoUtils.isYouTubeUrl('https://example.com/image.gif'), false);
      });

      test('should extract YouTube video ID correctly', () {
        expect(
          VideoUtils.extractYouTubeVideoId(
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          ),
          'dQw4w9WgXcQ',
        );
        expect(
          VideoUtils.extractYouTubeVideoId('https://youtu.be/dQw4w9WgXcQ'),
          'dQw4w9WgXcQ',
        );
        expect(
          VideoUtils.extractYouTubeVideoId(
            'https://youtube.com/embed/dQw4w9WgXcQ',
          ),
          'dQw4w9WgXcQ',
        );
        expect(
          VideoUtils.extractYouTubeVideoId('https://example.com/video.mp4'),
          null,
        );
      });

      test('should detect video files correctly', () {
        expect(VideoUtils.isVideoFile('https://example.com/video.mp4'), true);
        expect(VideoUtils.isVideoFile('https://example.com/video.webm'), true);
        expect(VideoUtils.isVideoFile('https://example.com/video.mov'), true);
        expect(VideoUtils.isVideoFile('https://example.com/image.gif'), false);
        expect(VideoUtils.isVideoFile('https://example.com/image.jpg'), false);
      });

      test('should determine video type correctly', () {
        expect(
          VideoUtils.getVideoType(
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          ),
          VideoType.youtube,
        );
        expect(
          VideoUtils.getVideoType('https://example.com/video.mp4'),
          VideoType.file,
        );
        expect(
          VideoUtils.getVideoType('https://example.com/image.gif'),
          VideoType.unknown,
        );
        expect(
          VideoUtils.getVideoType('https://example.com/image.jpg'),
          VideoType.unknown,
        );
      });
    });

    group('GIF Animation Support', () {
      test('should recognize animated image formats', () {
        // GIF файлы должны обрабатываться как изображения, но с поддержкой анимации
        expect(
          VideoUtils.getVideoType('https://example.com/animation.gif'),
          VideoType.unknown,
        );
        expect(
          VideoUtils.isVideoFile('https://example.com/animation.gif'),
          false,
        );

        // WebP анимации также должны поддерживаться
        expect(
          VideoUtils.getVideoType('https://example.com/animation.webp'),
          VideoType.unknown,
        );
        expect(
          VideoUtils.isVideoFile('https://example.com/animation.webp'),
          false,
        );
      });
    });
  });
}
