import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Storage Service Tests', () {
    test('should determine file type correctly', () {
      // Тестируем логику определения типов файлов
      expect(_determineFileType('jpg'), equals('image'));
      expect(_determineFileType('png'), equals('image'));
      expect(_determineFileType('gif'), equals('image'));
      expect(_determineFileType('heic'), equals('image'));
      expect(_determineFileType('heif'), equals('image'));

      expect(_determineFileType('mp4'), equals('video'));
      expect(_determineFileType('avi'), equals('video'));
      expect(_determineFileType('mov'), equals('video'));

      expect(_determineFileType('mp3'), equals('audio'));
      expect(_determineFileType('wav'), equals('audio'));
      expect(_determineFileType('ogg'), equals('audio'));
      expect(_determineFileType('m4a'), equals('audio'));

      expect(_determineFileType('pdf'), equals('document'));
      expect(_determineFileType('doc'), equals('document'));
      expect(_determineFileType('txt'), equals('document'));
      expect(_determineFileType('docx'), equals('document'));

      expect(_determineFileType('unknown'), equals('file'));
      expect(_determineFileType(''), equals('file'));
      expect(_determineFileType('xyz'), equals('file'));
    });

    test('should handle case insensitive extensions', () {
      expect(_determineFileType('JPG'), equals('image'));
      expect(_determineFileType('PNG'), equals('image'));
      expect(_determineFileType('MP4'), equals('video'));
      expect(_determineFileType('PDF'), equals('document'));
    });

    test('should extract correct MIME types', () {
      expect(_getMimeType('image', '.jpg'), equals('image/jpeg'));
      expect(_getMimeType('image', '.png'), equals('image/png'));
      expect(_getMimeType('video', '.mp4'), equals('video/mp4'));
      expect(_getMimeType('audio', '.mp3'), equals('audio/mpeg'));
      expect(_getMimeType('document', '.pdf'), equals('application/pdf'));
      expect(
        _getMimeType('file', '.unknown'),
        equals('application/octet-stream'),
      );
    });
  });
}

// Копируем логику из SupabaseStorageService для тестирования
String _determineFileType(String extension) {
  final ext = extension.toLowerCase();
  if ([
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'bmp',
    'tiff',
    'heic',
    'heif',
  ].contains(ext)) {
    return 'image';
  } else if (['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv'].contains(ext)) {
    return 'video';
  } else if (['mp3', 'wav', 'aac', 'flac', 'm4a', 'ogg'].contains(ext)) {
    return 'audio';
  } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].contains(ext)) {
    return 'document';
  } else {
    return 'file';
  }
}

String _getMimeType(String type, String extension) {
  final ext = extension.toLowerCase().replaceFirst('.', '');

  switch (type) {
    case 'image':
      switch (ext) {
        case 'jpg':
        case 'jpeg':
          return 'image/jpeg';
        case 'png':
          return 'image/png';
        case 'gif':
          return 'image/gif';
        case 'webp':
          return 'image/webp';
        case 'bmp':
          return 'image/bmp';
        case 'tiff':
          return 'image/tiff';
        case 'heic':
          return 'image/heic';
        case 'heif':
          return 'image/heif';
        default:
          return 'image/jpeg';
      }
    case 'video':
      switch (ext) {
        case 'mp4':
          return 'video/mp4';
        case 'avi':
          return 'video/x-msvideo';
        case 'mov':
          return 'video/quicktime';
        case 'mkv':
          return 'video/x-matroska';
        case 'webm':
          return 'video/webm';
        default:
          return 'video/mp4';
      }
    case 'audio':
      switch (ext) {
        case 'mp3':
          return 'audio/mpeg';
        case 'wav':
          return 'audio/wav';
        case 'aac':
          return 'audio/aac';
        case 'flac':
          return 'audio/flac';
        case 'm4a':
          return 'audio/mp4';
        case 'ogg':
          return 'audio/ogg';
        default:
          return 'audio/mpeg';
      }
    case 'document':
      switch (ext) {
        case 'pdf':
          return 'application/pdf';
        case 'doc':
          return 'application/msword';
        case 'docx':
          return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        case 'txt':
          return 'text/plain';
        case 'rtf':
          return 'application/rtf';
        default:
          return 'application/octet-stream';
      }
    default:
      return 'application/octet-stream';
  }
}
