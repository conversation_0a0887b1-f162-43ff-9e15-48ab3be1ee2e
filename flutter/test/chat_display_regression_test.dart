import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:luxury_app/features/ai_chat/ai_chat_view/ai_chat_screen.dart';
import 'package:luxury_app/features/ai_chat/ai_chat_view/chat_layout.dart';
import 'package:luxury_app/features/ai_chat/ai_chat_view/message_item.dart';
import 'package:luxury_app/features/ai_chat/data/ai_message.dart';

// Регрессионные тесты для предотвращения багов отображения чатов
void main() {
  group('Chat Display Regression Tests', () {
    testWidgets('AIChatContent widget should be created without errors', (
      tester,
    ) async {
      // Arrange
      const chatId = 1;

      // Act & Assert - просто проверяем что виджет создается без ошибок
      expect(() {
        const AIChatContent(chatId: chatId);
      }, returnsNormally);
    });

    testWidgets('ChatLayout should be created with empty messages', (
      tester,
    ) async {
      // Arrange
      final List<AIMessage> emptyMessages = [];
      final ScrollController scrollController = ScrollController();

      // Act & Assert - проверяем создание виджета
      expect(() {
        ChatLayout(
          messages: emptyMessages,
          scrollController: scrollController,
          inputBuilder: () => const SizedBox(),
        );
      }, returnsNormally);
    });

    testWidgets('ChatLayout should be created with messages', (tester) async {
      // Arrange
      final testMessages = [
        AIMessage(
          id: 1,
          role: 'user',
          content: 'Тестовое сообщение пользователя',
          chatId: 1,
          createdAt: DateTime.now(),
          attachments: [],
        ),
        AIMessage(
          id: 2,
          role: 'assistant',
          content: 'Ответ ассистента',
          chatId: 1,
          createdAt: DateTime.now(),
          attachments: [],
        ),
      ];
      final ScrollController scrollController = ScrollController();

      // Act & Assert - проверяем создание виджета без ошибок
      expect(() {
        ChatLayout(
          messages: testMessages,
          scrollController: scrollController,
          inputBuilder: () => const SizedBox(),
        );
      }, returnsNormally);
    });

    testWidgets('MessageItem should handle reasoning messages', (tester) async {
      // Arrange
      final reasoningMessage = AIMessage(
        id: 1,
        role: 'assistant',
        content: 'Результат рассуждений',
        chatId: 1,
        createdAt: DateTime.now(),
        attachments: [],
        metadata: {'reasoning_summary': 'Подробное рассуждение'},
      );

      // Act & Assert - проверяем создание виджета
      expect(() {
        MessageItem(message: reasoningMessage, isStreaming: false);
      }, returnsNormally);
    });

    testWidgets('MessageItem should handle streaming messages', (tester) async {
      // Arrange
      final streamingMessage = AIMessage(
        id: 1,
        role: 'assistant',
        content: 'Стримящееся сообщение...',
        chatId: 1,
        createdAt: DateTime.now(),
        attachments: [],
      );

      // Act & Assert - проверяем создание виджета
      expect(() {
        MessageItem(message: streamingMessage, isStreaming: true);
      }, returnsNormally);
    });

    test('AIMessage model should support reasoning metadata', () {
      // Arrange & Act
      final message = AIMessage(
        id: 1,
        role: 'assistant',
        content: 'Test content',
        chatId: 1,
        createdAt: DateTime.now(),
        attachments: [],
        metadata: {'reasoning_summary': 'Test reasoning'},
      );

      // Assert
      expect(message.metadata, isNotNull);
      expect(message.metadata!['reasoning_summary'], equals('Test reasoning'));
      expect(message.role, equals('assistant'));
      expect(message.content, equals('Test content'));
    });
  });
}
