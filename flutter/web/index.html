<!DOCTYPE html>
<html>
<head>
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Luxury Sound Team App">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

  <!-- PWA meta tags -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="format-detection" content="telephone=no">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-title" content="luxuryApp">
  
  <!-- iOS icons -->
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="152x152" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="180x180" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="167x167" href="icons/Icon-192.png">

  <!-- Theme color for browsers -->
  <meta name="theme-color" content="transparent">
  
  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>
  <link rel="icon" type="image/png" sizes="192x192" href="icons/Icon-192.png"/>
  <link rel="icon" type="image/png" sizes="512x512" href="icons/Icon-512.png"/>
  <link rel="icon" type="image/png" sizes="192x192" href="icons/Icon-maskable-192.png"/>
  <link rel="icon" type="image/png" sizes="512x512" href="icons/Icon-maskable-512.png"/>

  <!-- Определение шрифтов для веб -->
  <style>
    /* Определения @font-face удалены. Flutter будет использовать шрифты из pubspec.yaml */
    .spinner {
      width: 50px;
      height: 50px;
      border: 5px solid rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      border-top-color: #ffffff;
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    #loading-text {
      margin-top: 16px;
      font-family: 'Geologica', sans-serif;
      font-size: 16px;
      color: #ffffff;
    }
  </style>
</head>
<body>
  <!-- Индикатор загрузки -->
  <div id="loading">
    <div class="spinner"></div>
    <div id="loading-text">Загрузка...</div>
  </div>

  <!-- Загружаем Flutter Engine -->
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
