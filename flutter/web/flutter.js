// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/**
 * This script is used to load the Flutter web app.
 */
(function() {
  'use strict';

  // Alias for document.getElementById to save bytes.
  const el = id => document.getElementById(id);

  let serviceWorkerVersion = null;
  let serviceWorkerUrl = null;

  // Creates a <script> tag with the specified attributes and body.
  function createScriptTag(attributes, body) {
    const script = document.createElement('script');
    for (const key in attributes) {
      script.setAttribute(key, attributes[key]);
    }
    if (body) {
      script.appendChild(document.createTextNode(body));
    }
    return script;
  }

  // Injects a <script> tag into the document.
  function injectScript(attributes, body) {
    const script = createScriptTag(attributes, body);
    document.body.appendChild(script);
    return script;
  }

  // Loads the main.dart.js script.
  function loadMainDartJs() {
    if ('serviceWorker' in navigator) {
      // Service workers are supported. Use them.
      window.addEventListener('load', function() {
        // Register Flutter's service worker.
        if (serviceWorkerUrl) {
          navigator.serviceWorker.register(serviceWorkerUrl, {
            scope: './',
          }).then(function(reg) {
            function waitForActivation(serviceWorker) {
              serviceWorker.addEventListener('statechange', () => {
                if (serviceWorker.state === 'activated') {
                  console.log('Installed new service worker.');
                  _loadMainDartJs();
                }
              });
            }
            if (!reg.active && (reg.installing || reg.waiting)) {
              // No active web worker and we have installed or are installing
              // one for the first time. Simply wait for it to activate.
              waitForActivation(reg.installing || reg.waiting);
            } else if (!reg.active.scriptURL.endsWith(serviceWorkerVersion)) {
              // When the app updates the serviceWorkerVersion changes, so we
              // need to ask the service worker to update.
              console.log('New service worker available.');
              reg.update();
              waitForActivation(reg.installing);
            } else {
              // Existing service worker is still good.
              console.log('Loading app from service worker.');
              _loadMainDartJs();
            }
          });
        } else {
          // Service workers not supported. Just load the main.dart.js
          _loadMainDartJs();
        }
      });
    } else {
      // Service workers not supported. Just load the main.dart.js
      _loadMainDartJs();
    }
  }

  // Internal function that actually loads the main.dart.js script.
  function _loadMainDartJs() {
    console.log('Loading main.dart.js');
    injectScript({ src: 'main.dart.js', id: 'flutter-app' });
  }

  // Flutter loader API.
  window._flutter = window._flutter || {};
  window._flutter.loader = {
    // Инициализация загрузчика Flutter.
    load: function(options) {
      options = options || {};
      if (options.serviceWorker) {
        serviceWorkerVersion = options.serviceWorker.serviceWorkerVersion;
        serviceWorkerUrl = 'flutter_service_worker.js?v=' + serviceWorkerVersion;
      }
      
      // Загрузка основного скрипта Flutter.
      if (options.onEntrypointLoaded) {
        // Если указан обработчик загрузки точки входа, используем его.
        const engineInitializer = {
          initializeEngine: function() {
            return new Promise(function(resolve) {
              loadMainDartJs();
              window.addEventListener('flutter-first-frame', function() {
                resolve({
                  runApp: function() {
                    console.log('Running app');
                  }
                });
              });
            });
          }
        };
        
        // Вызываем обработчик загрузки точки входа.
        options.onEntrypointLoaded(engineInitializer);
      } else {
        // Если обработчик не указан, просто загружаем основной скрипт.
        loadMainDartJs();
      }
    }
  };
})(); 