'use strict';

const CACHE_NAME = 'flutter-app-cache';
const RESOURCES = {
    "version.json": "1d5c0194f6640e6a1b2324fdce1af66e",
    "index.html": "7918a8f8fd1fbce0e4022ed30bfe2441",
    "main.dart.js": "2abbc010aa9192194bff16dcfc8facba",
    "flutter.js": "6fef97aeca90b426343ba6c5c9dc5d4a",
    "favicon.ico": "e8aaf2be91832713c86753e45f5b6290",
    "icons/Icon-192.png": "ac9a721a12bbc803b44f645561ecb1e1",
    "icons/Icon-512.png": "96e752610906ba2a93c65f8abe1645f1",
    "manifest.json": "e2b9103a7f95aee01c4d0c8cf122f27d",
    "assets/": "058a3a02546be573d3c16958d3d28",
    "assets/fonts/": "af7ae505a9eed503f8b8e6982036873e"
};

self.addEventListener('install', function (event) {
    event.waitUntil(
        caches.open(CACHE_NAME).then(function (cache) {
            return cache.addAll(Object.keys(RESOURCES));
        })
    );
});

self.addEventListener('activate', function (event) {
    event.waitUntil(
        caches.keys().then(function (cacheName) {
            return Promise.all(
                cacheName.filter(function (cacheName) {
                    return cacheName.startsWith('flutter-app-') &&
                        cacheName != CACHE_NAME;
                }).map(function (cacheName) {
                    return caches.delete(cacheName);
                })
            );
        })
    );
});

self.addEventListener('fetch', function (event) {
    event.respondWith(
        caches.match(event.request).then(function (response) {
            return response || fetch(event.request);
        })
    );
});
