package com.lsound.luxuryapp

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.Manifest
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class MainActivity: FlutterActivity() {
    private val KEYCHAIN_CHANNEL = "com.lsound.luxuryapp/keychain"
    private val MIC_CHANNEL = "com.lsound.luxuryapp/mic"
    private val MIC_PERMISSION_REQUEST_CODE = 100
    private lateinit var credentialManager: CredentialManager
    private var pendingMicResult: MethodChannel.Result? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        credentialManager = CredentialManager(applicationContext)
        
        // Канал для работы с паролями
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, KEYCHAIN_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getCredentials" -> {
                    val credentials = credentialManager.getCredentials()
                    result.success(credentials)
                }
                "saveCredentials" -> {
                    val username = call.argument<String>("username") ?: ""
                    val password = call.argument<String>("password") ?: ""
                    
                    if (username.isEmpty() || password.isEmpty()) {
                        result.error("INVALID_ARGS", "Username or password is empty", null)
                        return@setMethodCallHandler
                    }
                    
                    val success = credentialManager.saveCredentials(username, password)
                    result.success(success)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
        
        // Канал для микрофона
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, MIC_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "requestMicPermission" -> {
                    requestMicrophonePermission(result)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
    
    private fun requestMicrophonePermission(result: MethodChannel.Result) {
        when {
            ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED -> {
                result.success(true)
            }
            else -> {
                pendingMicResult = result
                ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.RECORD_AUDIO), MIC_PERMISSION_REQUEST_CODE)
            }
        }
    }
    
    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == MIC_PERMISSION_REQUEST_CODE) {
            val granted = grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED
            pendingMicResult?.success(granted)
            pendingMicResult = null
        }
    }
} 