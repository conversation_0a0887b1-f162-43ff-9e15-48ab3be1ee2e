package com.lsound.luxuryapp

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import android.util.Log
import androidx.annotation.RequiresApi
import java.nio.charset.Charset
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec

/**
 * Класс для безопасного хранения учетных данных на Android
 */
class CredentialManager(private val context: Context) {

    private val TAG = "CredentialManager"
    private val KEYSTORE_PROVIDER = "AndroidKeyStore"
    private val KEY_ALIAS = "LuxuryAppCredentialKey"
    private val SHARED_PREFS_NAME = "LuxuryAppCredentials"
    private val USERNAME_KEY = "username"
    private val PASSWORD_KEY = "password"
    private val IV_KEY = "iv"
    
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE)
    
    init {
        // Убедимся, что ключ существует
        getOrCreateSecretKey()
    }

    /**
     * Сохраняет учетные данные
     */
    fun saveCredentials(username: String, password: String): Boolean {
        try {
            val secretKey = getOrCreateSecretKey()
            val cipher = Cipher.getInstance("AES/GCM/NoPadding")
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            
            val iv = cipher.iv
            val encryptedPassword = cipher.doFinal(password.toByteArray(Charset.forName("UTF-8")))
            
            sharedPreferences.edit()
                .putString(USERNAME_KEY, username)
                .putString(PASSWORD_KEY, Base64.encodeToString(encryptedPassword, Base64.DEFAULT))
                .putString(IV_KEY, Base64.encodeToString(iv, Base64.DEFAULT))
                .apply()
            
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error saving credentials", e)
            return false
        }
    }

    /**
     * Получает сохраненные учетные данные
     */
    fun getCredentials(): HashMap<String, String>? {
        try {
            val username = sharedPreferences.getString(USERNAME_KEY, null) ?: return null
            val encryptedPassword = sharedPreferences.getString(PASSWORD_KEY, null) ?: return null
            val ivString = sharedPreferences.getString(IV_KEY, null) ?: return null
            
            val secretKey = getOrCreateSecretKey()
            val cipher = Cipher.getInstance("AES/GCM/NoPadding")
            val iv = Base64.decode(ivString, Base64.DEFAULT)
            val spec = GCMParameterSpec(128, iv)
            
            cipher.init(Cipher.DECRYPT_MODE, secretKey, spec)
            val passwordBytes = Base64.decode(encryptedPassword, Base64.DEFAULT)
            val decryptedPassword = String(cipher.doFinal(passwordBytes), Charset.forName("UTF-8"))
            
            val result = HashMap<String, String>()
            result["username"] = username
            result["password"] = decryptedPassword
            
            return result
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving credentials", e)
            return null
        }
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun getOrCreateSecretKey(): SecretKey {
        val keyStore = KeyStore.getInstance(KEYSTORE_PROVIDER)
        keyStore.load(null)
        
        if (!keyStore.containsAlias(KEY_ALIAS)) {
            val keyGenerator = KeyGenerator.getInstance(
                KeyProperties.KEY_ALGORITHM_AES,
                KEYSTORE_PROVIDER
            )
            
            val keyGenParameterSpec = KeyGenParameterSpec.Builder(
                KEY_ALIAS,
                KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
            )
                .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                .setKeySize(256)
                .build()
            
            keyGenerator.init(keyGenParameterSpec)
            return keyGenerator.generateKey()
        }
        
        return (keyStore.getEntry(KEY_ALIAS, null) as KeyStore.SecretKeyEntry).secretKey
    }
} 