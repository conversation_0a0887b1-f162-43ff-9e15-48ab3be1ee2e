import Cocoa
import FlutterMacOS
import Foundation
import Security
import AVFoundation

@main
class AppDelegate: FlutterAppDelegate {
    override func applicationDidFinishLaunching(_ notification: Notification) {
        // Регистрируем методы для работы с паролями
        guard let controller = mainFlutterWindow?.contentViewController as? FlutterViewController else {
            return
        }
        
        let channel = FlutterMethodChannel(
            name: "com.lsound.luxuryapp/keychain",
            binaryMessenger: controller.engine.binaryMessenger
        )
        
        channel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
            case "getCredentials":
                self?.getCredentials(result: result)
            case "saveCredentials":
                guard let arguments = call.arguments as? [String: Any],
                      let username = arguments["username"] as? String,
                      let password = arguments["password"] as? String else {
                    result(FlutterError(code: "INVALID_ARGS", message: "Invalid arguments", details: nil))
                    return
                }
                self?.saveCredentials(username: username, password: password, result: result)
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        
        // Канал для запроса разрешения на микрофон из Flutter
        let micChannel = FlutterMethodChannel(
            name: "com.lsound.luxuryapp/mic",
            binaryMessenger: controller.engine.binaryMessenger
        )
        micChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
            if call.method == "requestMicPermission" {
                // Сначала проверяем текущий статус
                let authStatus = AVCaptureDevice.authorizationStatus(for: .audio)
                
                switch authStatus {
                case .authorized:
                    result(true)
                case .denied, .restricted:
                    result(false)
                case .notDetermined:
                    // Запрашиваем разрешение
                    AVCaptureDevice.requestAccess(for: .audio) { granted in
                        DispatchQueue.main.async {
                            result(granted)
                        }
                    }
                @unknown default:
                    result(false)
                }
            } else {
                result(FlutterMethodNotImplemented)
            }
        }
        
        super.applicationDidFinishLaunching(notification)

        // Запросить доступ к микрофону на macOS
        AVCaptureDevice.requestAccess(for: .audio) { granted in
            print("Microphone access granted: \(granted)")
        }
    }
    
    private func getCredentials(result: @escaping FlutterResult) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassInternetPassword,
            kSecAttrServer as String: "app.lsound.info",
            kSecReturnAttributes as String: true,
            kSecReturnData as String: true
        ]
        
        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)
        
        guard status != errSecItemNotFound else {
            result(nil)
            return
        }
        
        guard status == errSecSuccess else {
            result(FlutterError(code: "KEYCHAIN_ERROR", message: "Failed to retrieve from keychain", details: nil))
            return
        }
        
        guard let existingItem = item as? [String: Any],
              let passwordData = existingItem[kSecValueData as String] as? Data,
              let password = String(data: passwordData, encoding: .utf8),
              let account = existingItem[kSecAttrAccount as String] as? String else {
            result(FlutterError(code: "PARSING_ERROR", message: "Failed to parse keychain result", details: nil))
            return
        }
        
        result(["username": account, "password": password])
    }
    
    private func saveCredentials(username: String, password: String, result: @escaping FlutterResult) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassInternetPassword,
            kSecAttrServer as String: "app.lsound.info",
            kSecAttrAccount as String: username,
            kSecValueData as String: password.data(using: .utf8)!,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlocked
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status == errSecDuplicateItem {
            // Обновляем существующую запись
            let updateQuery: [String: Any] = [
                kSecClass as String: kSecClassInternetPassword,
                kSecAttrServer as String: "app.lsound.info",
                kSecAttrAccount as String: username
            ]
            
            let updateAttributes: [String: Any] = [
                kSecValueData as String: password.data(using: .utf8)!
            ]
            
            let updateStatus = SecItemUpdate(updateQuery as CFDictionary, updateAttributes as CFDictionary)
            
            guard updateStatus == errSecSuccess else {
                result(FlutterError(code: "UPDATE_ERROR", message: "Failed to update keychain item", details: nil))
                return
            }
            
            result(true)
            return
        }
        
        guard status == errSecSuccess else {
            result(FlutterError(code: "SAVE_ERROR", message: "Failed to save to keychain", details: nil))
            return
        }
        
        result(true)
    }

    override func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }

    override func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }
}
