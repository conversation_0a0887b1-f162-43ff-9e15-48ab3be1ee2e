import Flutter
import UIKit
import Security
import AVFoundation

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> <PERSON>ol {
    // Регистрируем методы для работы с паролями
    let controller = window?.rootViewController as! FlutterViewController
    
    let channel = FlutterMethodChannel(
        name: "com.lsound.luxuryapp/keychain",
        binaryMessenger: controller.binaryMessenger
    )
    
    channel.setMethodCallHandler { [weak self] call, result in
        switch call.method {
        case "getCredentials":
            self?.getCredentials(result: result)
        case "saveCredentials":
            guard let arguments = call.arguments as? [String: Any],
                  let username = arguments["username"] as? String,
                  let password = arguments["password"] as? String else {
                result(FlutterError(code: "INVALID_ARGS", message: "Invalid arguments", details: nil))
                return
            }
            self?.saveCredentials(username: username, password: password, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    // Канал для микрофона
    let micChannel = FlutterMethodChannel(
        name: "com.lsound.luxuryapp/mic",
        binaryMessenger: controller.binaryMessenger
    )
    
    micChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
        if call.method == "requestMicPermission" {
            let authStatus = AVCaptureDevice.authorizationStatus(for: .audio)
            
            switch authStatus {
            case .authorized:
                result(true)
            case .denied, .restricted:
                result(false)
            case .notDetermined:
                AVCaptureDevice.requestAccess(for: .audio) { granted in
                    DispatchQueue.main.async {
                        result(granted)
                    }
                }
            @unknown default:
                result(false)
            }
        } else {
            result(FlutterMethodNotImplemented)
        }
    }
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  private func getCredentials(result: @escaping FlutterResult) {
      let query: [String: Any] = [
          kSecClass as String: kSecClassInternetPassword,
          kSecAttrServer as String: "app.lsound.info",
          kSecReturnAttributes as String: true,
          kSecReturnData as String: true
      ]
      
      var item: CFTypeRef?
      let status = SecItemCopyMatching(query as CFDictionary, &item)
      
      guard status != errSecItemNotFound else {
          result(nil)
          return
      }
      
      guard status == errSecSuccess else {
          result(FlutterError(code: "KEYCHAIN_ERROR", message: "Failed to retrieve from keychain", details: nil))
          return
      }
      
      guard let existingItem = item as? [String: Any],
            let passwordData = existingItem[kSecValueData as String] as? Data,
            let password = String(data: passwordData, encoding: .utf8),
            let account = existingItem[kSecAttrAccount as String] as? String else {
          result(FlutterError(code: "PARSING_ERROR", message: "Failed to parse keychain result", details: nil))
          return
      }
      
      result(["username": account, "password": password])
  }
  
  private func saveCredentials(username: String, password: String, result: @escaping FlutterResult) {
      let query: [String: Any] = [
          kSecClass as String: kSecClassInternetPassword,
          kSecAttrServer as String: "app.lsound.info",
          kSecAttrAccount as String: username,
          kSecValueData as String: password.data(using: .utf8)!,
          kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlocked
      ]
      
      let status = SecItemAdd(query as CFDictionary, nil)
      
      if status == errSecDuplicateItem {
          // Обновляем существующую запись
          let updateQuery: [String: Any] = [
              kSecClass as String: kSecClassInternetPassword,
              kSecAttrServer as String: "app.lsound.info",
              kSecAttrAccount as String: username
          ]
          
          let updateAttributes: [String: Any] = [
              kSecValueData as String: password.data(using: .utf8)!
          ]
          
          let updateStatus = SecItemUpdate(updateQuery as CFDictionary, updateAttributes as CFDictionary)
          
          guard updateStatus == errSecSuccess else {
              result(FlutterError(code: "UPDATE_ERROR", message: "Failed to update keychain item", details: nil))
              return
          }
          
          result(true)
          return
      }
      
      guard status == errSecSuccess else {
          result(FlutterError(code: "SAVE_ERROR", message: "Failed to save to keychain", details: nil))
          return
      }
      
      result(true)
  }
}
