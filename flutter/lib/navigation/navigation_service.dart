import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:luxury_app/app_provider.dart';
import 'package:luxury_app/app_state.dart';

import 'route_config.dart';

/// Унифицированный сервис навигации для централизованного управления переходами между экранами
class NavigationService {
  // Приватный конструктор для предотвращения создания экземпляров
  NavigationService._();

  // --- Статические методы для навигации ---

  /// Переход на экран AI через контекст
  static void navigateToAI(BuildContext context, String chatId) {
    context.goNamed(RouteConfig.aiRoute, pathParameters: {'chatId': chatId});
  }

  /// Переход на экран Wiki через контекст
  static void navigateToWiki(
    BuildContext context,
    String pageId, {
    String? name,
  }) {
    context.goNamed(
      RouteConfig.wikiRoute,
      pathParameters: {'pageId': pageId},
      queryParameters: name != null ? {'name': name} : {},
    );
  }

  /// Переход на экран настроек через контекст
  static void navigateToSettings(BuildContext context) {
    context.goNamed(RouteConfig.settingsRoute);
  }

  /// Переход на экран новостей через контекст
  static void navigateToNews(BuildContext context) {
    context.goNamed(RouteConfig.newsRoute);
  }

  /// Изменение режима drawer на Wiki
  static void changeToWikiMode(WidgetRef ref) {
    ref.read(appProvider.notifier).changeDrawerMode(DrawerMode.wiki);
  }

  /// Изменение режима drawer на AI
  static void changeToAIMode(WidgetRef ref) {
    ref.read(appProvider.notifier).changeDrawerMode(DrawerMode.chat);
  }
}
