import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/navigation/navigation_service.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/constants/strings.dart';
import 'package:luxury_app/shared/widgets/drawer_list_item.dart';

/// Виджет нижней части боковой панели
class DrawerFooter extends StatelessWidget {
  /// Ключ Scaffold
  final GlobalKey<ScaffoldState> scaffoldKey;

  /// Контекст приложения
  final BuildContext context;

  /// Создает нижнюю часть боковой панели
  const DrawerFooter({
    super.key,
    required this.scaffoldKey,
    required this.context,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: Safe<PERSON><PERSON>(
        child: Column(
          children: [
            DrawerListItem(
              title: 'Новости',
              leading: Icon(
                LucideIcons.newspaper,
                size: AppSizes.navIconSize * AppSizes.scaleSmall,
              ),
              onTap: () {
                if (MediaQuery.of(context).size.width < 600) {
                  scaffoldKey.currentState?.closeDrawer();
                }
                NavigationService.navigateToNews(context);
              },
            ),
            Divider(
              height: 0,
              thickness: 1,
              color: Theme.of(context).colorScheme.outline.withAlpha(30),
            ),
            DrawerListItem(
              title: AppText.settingsTitle,
              leading: Icon(
                LucideIcons.settings,
                size: AppSizes.navIconSize * AppSizes.scaleSmall,
              ),
              onTap: () {
                if (MediaQuery.of(context).size.width < 600) {
                  scaffoldKey.currentState?.closeDrawer();
                }
                NavigationService.navigateToSettings(context);
              },
            ),
          ],
        ),
      ),
    );
  }
}
