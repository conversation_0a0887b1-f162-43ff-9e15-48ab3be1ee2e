import 'package:flutter/material.dart';
import 'package:luxury_app/navigation/navigation_service.dart';

class PleaseSignIn extends StatelessWidget {
  const PleaseSignIn({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
        constraints: const BoxConstraints(maxWidth: 320),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: theme.colorScheme.surfaceContainerHighest.withAlpha(200),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '🤨',
              style: TextStyle(fontSize: 40, color: theme.colorScheme.primary),
            ),
            const SizedBox(height: 20),
            Text(
              'Мы знакомы?',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Войдите в аккаунт для доступа к чатам',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            FilledButton.icon(
              onPressed: () {
                NavigationService.navigateToSettings(context);
              },
              icon: const Icon(Icons.settings, size: 18),
              label: const Text('Настройки'),
              style: FilledButton.styleFrom(
                minimumSize: const Size.fromHeight(44),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
