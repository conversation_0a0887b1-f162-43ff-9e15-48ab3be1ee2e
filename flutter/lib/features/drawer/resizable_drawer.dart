import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_provider.dart';

import 'app_drawer.dart';

class ResizableDrawer extends ConsumerStatefulWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;
  const ResizableDrawer({super.key, required this.scaffoldKey});

  @override
  ConsumerState<ResizableDrawer> createState() => _ResizableDrawerState();
}

class _ResizableDrawerState extends ConsumerState<ResizableDrawer> {
  static const double minDrawerWidth = 280;
  static const double maxDrawerWidth = 400;
  bool _dragging = false;

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(appProvider);
    final drawerWidth = state.drawerWidth;

    return MouseRegion(
      cursor:
          _dragging
              ? SystemMouseCursors.resizeColumn
              : SystemMouseCursors.basic,
      child: Row(
        children: [
          Container(
            width: drawerWidth,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              border: Border(
                right: BorderSide(
                  color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
                  width: 0.5,
                ),
              ),
            ),
            child: AppDrawer(
              scaffoldKey: widget.scaffoldKey,
              roundedCorners: false,
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onHorizontalDragStart: (_) {
              setState(() => _dragging = true);
            },
            onHorizontalDragUpdate: (details) {
              final newWidth = (drawerWidth + details.delta.dx).clamp(
                minDrawerWidth,
                maxDrawerWidth,
              );
              ref.read(appProvider.notifier).changeDrawerWidth(newWidth);
            },
            onHorizontalDragEnd: (_) {
              setState(() => _dragging = false);
            },
            child: Container(
              width: 4,
              height: double.infinity,
              color: Colors.transparent,
              child: MouseRegion(
                cursor: SystemMouseCursors.resizeColumn,
                child: Container(
                  width: 1,
                  margin: const EdgeInsets.symmetric(horizontal: 1.5),
                  color:
                      _dragging
                          ? Theme.of(
                            context,
                          ).colorScheme.primary.withValues(alpha: 0.3)
                          : Colors.transparent,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
