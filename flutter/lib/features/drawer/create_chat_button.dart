import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/widgets/drawer_list_item.dart';

class CreateChatButton extends StatelessWidget {
  final Function() onCreateChat;

  const CreateChatButton({super.key, required this.onCreateChat});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: DrawerListItem(
        title: 'Создать новый чат',
        titleColor: Theme.of(context).colorScheme.primary,
        leading: Icon(
          LucideIcons.plus,
          size: AppSizes.navIconSize * AppSizes.scaleSmall,
          color: Theme.of(context).colorScheme.primary,
        ),
        onTap: onCreateChat,
      ),
    );
  }
}
