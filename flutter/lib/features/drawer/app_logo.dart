import 'package:flutter/material.dart';
import 'package:luxury_app/shared/constants/strings.dart';

/// Виджет логотипа приложения
import 'package:luxury_app/shared/utils/confetti.dart';
import 'package:luxury_app/theme/theme.dart';

class AppLogo extends StatefulWidget {
  /// Создает логотип приложения
  const AppLogo({super.key});

  @override
  State<AppLogo> createState() => _AppLogoState();
}

class _AppLogoState extends State<AppLogo> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showConfettiAnywhere(context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 12.0),
        margin: const EdgeInsets.only(right: 4.0),
        decoration: const BoxDecoration(color: AppColors.brandPurple),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppText.appTitlePrefix,
              style: const TextStyle(
                fontSize: 12.0,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              AppText.appTitleSuffix,
              style: const TextStyle(
                fontSize: 12.0,
                color: AppColors.brandGold,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
