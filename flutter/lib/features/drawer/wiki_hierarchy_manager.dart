import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/features/drawer/wiki_hierarchy.dart';
import 'package:luxury_app/features/wiki/wiki_data/wiki_models.dart';
import 'package:luxury_app/features/wiki/wiki_provider.dart';

/// Менеджер для WikiHierarchy: связывает провайдер и presentation-слой
class WikiHierarchyManager extends ConsumerStatefulWidget {
  final String? selectedFileId;
  final ValueChanged<WikiFile> onFileTap;

  const WikiHierarchyManager({
    super.key,
    required this.onFileTap,
    this.selectedFileId,
  });

  @override
  ConsumerState<WikiHierarchyManager> createState() =>
      _WikiHierarchyManagerState();
}

class _WikiHierarchyManagerState extends ConsumerState<WikiHierarchyManager> {
  late final TextEditingController searchController;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();

    // Инициализируем загрузку данных при первом создании
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeWikiData();
    });
  }

  /// Инициализирует загрузку данных Wiki
  void _initializeWikiData() {
    final wikiState = ref.read(wikiProvider);
    // Если данные не загружены и нет активной загрузки, запускаем загрузку
    if (wikiState.folders.isEmpty && !wikiState.isLoading) {
      ref.read(wikiProvider.notifier).loadFolders();
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final wikiState = ref.watch(wikiProvider);
    final hierarchyData = _WikiHierarchyData.fromState(wikiState);

    // Добавляем отладочную информацию
    debugPrint(
      '🔍 WikiHierarchyManager build: isLoading=${hierarchyData.isLoading}, folders.length=${hierarchyData.folders.length}, error=${hierarchyData.error}',
    );

    void handleSearch(String query) {
      ref.read(wikiProvider.notifier).searchWiki(query);
    }

    void handleExpandCollapse() {
      ref.read(wikiProvider.notifier).toggleAllFolders();
    }

    void handleClearSearch() {
      searchController.clear();
      handleSearch('');
    }

    void handleToggleFolder(String id) {
      ref.read(wikiProvider.notifier).toggleFolder(id);
    }

    return WikiHierarchy(
      folders: hierarchyData.folders,
      isLoading: hierarchyData.isLoading,
      error: hierarchyData.error,
      isSearching: hierarchyData.isSearching,
      selectedFileId: widget.selectedFileId,
      allExpanded: hierarchyData.allExpanded,
      expandedFolders: hierarchyData.expandedFolders,
      searchController: searchController,
      onSearch: handleSearch,
      onExpandCollapse: handleExpandCollapse,
      onClearSearch: handleClearSearch,
      onToggleFolder: handleToggleFolder,
      onFileTap: widget.onFileTap,
    );
  }
}

/// Оптимизированная структура данных для Wiki иерархии
/// Позволяет избежать ненужных перестроений при переходе между состояниями
class _WikiHierarchyData {
  final List<WikiFolder> folders;
  final bool allExpanded;
  final bool isSearching;
  final Set<String> expandedFolders;
  final bool isLoading;
  final String? error;

  const _WikiHierarchyData({
    required this.folders,
    required this.allExpanded,
    required this.isSearching,
    required this.expandedFolders,
    required this.isLoading,
    this.error,
  });

  /// Создает данные из WikiState
  factory _WikiHierarchyData.fromState(WikiState wikiState) {
    return _WikiHierarchyData(
      folders: wikiState.folders,
      allExpanded: wikiState.allFoldersExpanded,
      isSearching: wikiState.isSearching,
      expandedFolders: wikiState.expandedFolders,
      isLoading: wikiState.isLoading,
      error: wikiState.error,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is _WikiHierarchyData &&
        other.folders == folders &&
        other.allExpanded == allExpanded &&
        other.isSearching == isSearching &&
        other.expandedFolders == expandedFolders &&
        other.isLoading == isLoading &&
        other.error == error;
  }

  @override
  int get hashCode {
    return folders.hashCode ^
        allExpanded.hashCode ^
        isSearching.hashCode ^
        expandedFolders.hashCode ^
        isLoading.hashCode ^
        error.hashCode;
  }
}
