/// Модель новостной записи
class NewsItem {
  /// Уникальный идентификатор новости
  final int id;
  
  /// Содержимое новости в формате Markdown
  final String content;
  
  /// Дата и время создания новости в формате ISO 8601
  final DateTime createdAt;

  /// Конструктор новостной записи
  const NewsItem({
    required this.id,
    required this.content,
    required this.createdAt,
  });

  /// Создание объекта из JSON
  factory NewsItem.fromJson(Map<String, dynamic> json) {
    return NewsItem(
      id: json['id'] as int,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// Преобразование объекта в JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
