import 'package:luxury_app/features/news/news_item.dart';

/// Интерфейс репозитория для работы с новостями
abstract class NewsRepository {
  /// Получение списка всех новостей
  ///
  /// Параметр [forceRefresh] указывает, нужно ли принудительно обновить данные
  Future<List<NewsItem>> getNews({bool forceRefresh = false});

  /// Подписка на real-time обновления новостей
  Stream<List<NewsItem>> subscribeToNews();
}
