import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/features/news/news_item.dart';
import 'package:luxury_app/features/news/news_provider.dart';
import 'package:luxury_app/shared/utils/date_formatter.dart';
import 'package:luxury_app/shared/widgets/markdown_renderer.dart';
import 'package:luxury_app/shared/widgets/state_widgets.dart';

/// Виджет для отображения новостей
class NewsContent extends ConsumerStatefulWidget {
  const NewsContent({super.key});

  @override
  ConsumerState<NewsContent> createState() => _NewsContentState();
}

class _NewsContentState extends ConsumerState<NewsContent> {
  @override
  Widget build(BuildContext context) {
    final newsState = ref.watch(newsProvider);

    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: DataStateWidget<List<NewsItem>>(
            isLoading: newsState.isLoading,
            error: newsState.error,
            data: newsState.data,
            onRetry: () => ref.read(newsProvider.notifier).refreshNews(),
            emptyMessage: 'Нет новостей',
            dataBuilder:
                (newsList) =>
                    _buildNewsList(context, newsList, newsState.isLoading),
          ),
        ),
      ),
    );
  }

  Widget _buildNewsList(
    BuildContext context,
    List<NewsItem> newsList,
    bool isLoading,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isLoading) const LinearProgressIndicator(),
        const SizedBox(height: 16),
        for (int i = 0; i < newsList.length; i++) ...[
          _buildNewsItem(context, newsList[i]),
          if (i < newsList.length - 1) const Divider(height: 48),
        ],
      ],
    );
  }

  Widget _buildNewsItem(BuildContext context, NewsItem newsItem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MarkdownRenderer(
          data: newsItem.content,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
        ),
        Align(
          alignment: Alignment.centerRight,
          child: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              DateFormatter.formatDateTime(newsItem.createdAt),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withAlpha(150),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
