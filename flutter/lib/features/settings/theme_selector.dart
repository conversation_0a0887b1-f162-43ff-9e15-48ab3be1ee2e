import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/theme/theme.dart';
import 'package:luxury_app/theme/theme_provider.dart';

/// Компонент выбора темы оформления
class ThemeSelector extends ConsumerWidget {
  const ThemeSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentThemeMode = ref.watch(themeProvider);
    final currentTheme = _themeModeToChoice(currentThemeMode);

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: AppSizes.paddingM,
        vertical: AppSizes.paddingXL,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [_buildThemeOptions(context, ref, currentTheme)],
      ),
    );
  }

  Widget _buildThemeOptions(
    BuildContext context,
    WidgetRef ref,
    ThemeChoice currentTheme,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildThemeOption(
          context: context,
          ref: ref,
          theme: ThemeChoice.light,
          icon: LucideIcons.sun,
          isSelected: currentTheme == ThemeChoice.light,
        ),
        _buildThemeOption(
          context: context,
          ref: ref,
          theme: ThemeChoice.dark,
          icon: LucideIcons.moon,
          isSelected: currentTheme == ThemeChoice.dark,
        ),
        _buildThemeOption(
          context: context,
          ref: ref,
          theme: ThemeChoice.system,
          icon: LucideIcons.monitor,
          isSelected: currentTheme == ThemeChoice.system,
        ),
      ],
    );
  }

  Widget _buildThemeOption({
    required BuildContext context,
    required WidgetRef ref,
    required ThemeChoice theme,
    required IconData icon,
    required bool isSelected,
  }) {
    final colors = Theme.of(context).colorScheme;

    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isSelected ? colors.primary.withAlpha(25) : Colors.transparent,
      ),
      child: IconButton(
        iconSize: 24,
        padding: EdgeInsets.all(AppSizes.paddingM),
        icon: Icon(icon),
        color: isSelected ? colors.primary : colors.onSurfaceVariant,
        onPressed: () => _handleThemeChange(ref, theme),
      ),
    );
  }

  void _handleThemeChange(WidgetRef ref, ThemeChoice theme) {
    final themeNotifier = ref.read(themeProvider.notifier);

    switch (theme) {
      case ThemeChoice.light:
        themeNotifier.changeTheme(ThemeMode.light);
        break;
      case ThemeChoice.dark:
        themeNotifier.changeTheme(ThemeMode.dark);
        break;
      case ThemeChoice.system:
        themeNotifier.changeTheme(ThemeMode.system);
        break;
    }
  }

  ThemeChoice _themeModeToChoice(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return ThemeChoice.light;
      case ThemeMode.dark:
        return ThemeChoice.dark;
      case ThemeMode.system:
        return ThemeChoice.system;
    }
  }
}
