import 'package:flutter/material.dart';
import 'package:luxury_app/core/services/cache_service.dart';

/// Виджет для отображения информации о кэше и очистки кэша
class CacheInfoWidget extends StatefulWidget {
  const CacheInfoWidget({super.key});

  @override
  State<CacheInfoWidget> createState() => _CacheInfoWidgetState();
}

class _CacheInfoWidgetState extends State<CacheInfoWidget> {
  int _cacheSize = 0;
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadCacheInfo();
  }

  Future<void> _loadCacheInfo() async {
    final box = CacheService.instance.box;
    if (box == null) {
      setState(() {
        _cacheSize = 0;
        _loading = false;
      });
      return;
    }
    int size = 0;
    for (final key in box.keys) {
      final value = box.get(key);
      if (value is String) {
        size += value.length;
      } else if (value is List) {
        size += value.length * 8;
      } else if (value is Map) {
        size += value.length * 16;
      } else if (value != null) {
        size += 32;
      }
    }
    setState(() {
      _cacheSize = size;
      _loading = false;
    });
  }

  Future<void> _clearCache() async {
    setState(() {
      _loading = true;
    });

    try {
      await CacheService.instance.clearAllCache();
      await _loadCacheInfo();

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Кэш успешно очищен')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Ошибка очистки кэша: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }
    return Column(
      children: [
        const Divider(height: 32),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 4),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final showRow = constraints.maxWidth > 350;
              final textBlock = Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Кэш приложения',
                    style: TextStyle(
                      fontSize: 13,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    '${(_cacheSize / 1024).toStringAsFixed(2)} КБ',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              );
              final clearBtn = TextButton.icon(
                icon: const Icon(
                  Icons.delete_outline,
                  size: 18,
                  color: Colors.red,
                ),
                label: const Text(
                  'Очистить',
                  style: TextStyle(color: Colors.red),
                ),
                style: TextButton.styleFrom(
                  minimumSize: const Size(0, 36),
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                ),
                onPressed: _clearCache,
              );
              if (showRow) {
                return Row(
                  children: [
                    Flexible(child: textBlock),
                    const SizedBox(width: 8),
                    clearBtn,
                  ],
                );
              } else {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [textBlock, const SizedBox(height: 8), clearBtn],
                );
              }
            },
          ),
        ),
      ],
    );
  }
}
