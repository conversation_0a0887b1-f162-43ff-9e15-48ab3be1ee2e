import 'package:luxury_app/features/settings/auth/auth_models/auth_tokens.dart';
import 'package:luxury_app/features/settings/auth/auth_models/user.dart';

/// Интерфейс репозитория аутентификации
abstract class AuthRepository {
  /// Загружает сохранённый токен и email из кеша (для инициализации ApiService)
  Future<void> loadSavedToken();
  /// Регистрирует нового пользователя
  Future<User> register(String email, String password, {String? phone});

  /// Выполняет вход пользователя
  Future<AuthTokens> login(
    String email,
    String password, {
    bool rememberMe = true,
  });

  /// Выполняет выход пользователя
  Future<void> logout();

  /// Проверяет, авторизован ли пользователь
  Future<bool> isAuthenticated();

  /// Получает текущего пользователя
  Future<User?> getCurrentUser();

  /// Получает текущий токен доступа
  Future<String?> getAccessToken();
}
