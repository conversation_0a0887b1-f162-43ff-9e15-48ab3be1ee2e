import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as sb;

import '../../../../core/services/cache_service.dart';
import '../../../../core/services/supabase_service.dart';
import '../auth_models/auth_tokens.dart';
import '../auth_models/user.dart';
import 'auth_cache_service.dart';
import 'auth_repository.dart';

/// Реализация AuthRepository с использованием Supabase
class SupabaseAuthRepository implements AuthRepository {
  final SupabaseService _supabaseService;
  final AuthCacheService _authCacheService;
  final CacheService _cacheService;

  SupabaseAuthRepository(
    this._supabaseService,
    this._authCacheService,
    this._cacheService,
  );

  @override
  Future<void> loadSavedToken() async {
    try {
      // В Supabase токены управляются автоматически
      // Проверяем есть ли активная сессия
      final session = _supabaseService.currentSession;

      if (session != null && session.accessToken.isNotEmpty) {
        if (kDebugMode) {
          debugPrint('✅ Найдена активная сессия Supabase');
        }

        // Сохраняем email в кэш для совместимости
        final user = _supabaseService.currentUser;
        if (user?.email != null) {
          await _authCacheService.saveUserEmail(user!.email!);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка загрузки сохраненного токена: $e');
      }
    }
  }

  @override
  Future<User> register(String email, String password, {String? phone}) async {
    try {
      final response = await _supabaseService.signUp(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception(
          'Не удалось создать пользователя. Проверьте email на подтверждение.',
        );
      }

      // Сохраняем email в кэш
      await _authCacheService.saveUserEmail(email);

      // Очищаем кэш чатов при новой регистрации
      await _cacheService.clearAllChatsData();

      return _supabaseUserToAppUser(response.user!);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка API: $e');
      }
      rethrow;
    }
  }

  @override
  Future<AuthTokens> login(
    String email,
    String password, {
    bool rememberMe = true,
  }) async {
    try {
      final response = await _supabaseService.signIn(
        email: email,
        password: password,
      );

      if (response.session == null) {
        throw Exception('Не удалось получить сессию после входа');
      }

      // Сохраняем email в кэш
      await _authCacheService.saveUserEmail(email);

      // Очищаем кэш чатов при новой авторизации
      // чтобы избежать отображения чатов другого пользователя
      await _cacheService.clearAllChatsData();

      return AuthTokens(
        accessToken: response.session!.accessToken,
        tokenType: 'Bearer',
        expiresIn: response.session!.expiresIn,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка API: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> logout() async {
    await _supabaseService.signOut();
    await _authCacheService.clearAuthData();
    // Очищаем также кэш чатов
    await _cacheService.clearAllChatsData();
  }

  @override
  Future<bool> isAuthenticated() async {
    return _supabaseService.isAuthenticated;
  }

  @override
  Future<User?> getCurrentUser() async {
    try {
      final supabaseUser = _supabaseService.currentUser;

      if (supabaseUser == null) {
        // Нет активной сессии, очищаем данные
        await logout();
        return null;
      }

      return _supabaseUserToAppUser(supabaseUser);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка получения текущего пользователя: $e');
      }
      // При ошибке также очищаем данные
      await logout();
      return null;
    }
  }

  @override
  Future<String?> getAccessToken() async {
    final session = _supabaseService.currentSession;
    return session?.accessToken;
  }

  /// Сброс пароля
  Future<void> resetPassword({required String email}) async {
    try {
      await _supabaseService.resetPassword(email: email);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка API: $e');
      }
      rethrow;
    }
  }

  /// Подписка на изменения состояния аутентификации
  Stream<sb.AuthState> get authStateChanges =>
      _supabaseService.authStateChanges;

  /// Преобразование Supabase User в модель приложения
  User _supabaseUserToAppUser(sb.User supabaseUser) {
    return User(
      id: supabaseUser.id,
      email: supabaseUser.email ?? '',
      phone: supabaseUser.phone,
      role: supabaseUser.userMetadata?['role'] as String? ?? 'user',
      createdAt: DateTime.parse(supabaseUser.createdAt),
      metadata: supabaseUser.userMetadata ?? {},
    );
  }
}
