import 'package:equatable/equatable.dart';

/// Модель пользователя
class User extends Equatable {
  /// Уникальный идентификатор пользователя
  final String id;
  
  /// Email пользователя
  final String email;
  
  /// Телефон пользователя (опционально)
  final String? phone;
  
  /// Роль пользователя
  final String role;
  
  /// Дата создания пользователя
  final DateTime createdAt;
  
  /// Дополнительные метаданные пользователя
  final Map<String, dynamic> metadata;

  /// Создает модель пользователя
  const User({
    required this.id,
    required this.email,
    this.phone,
    required this.role,
    required this.createdAt,
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [id, email, phone, role, createdAt, metadata];

  /// Создает копию модели с новыми значениями
  User copyWith({
    String? id,
    String? email,
    String? phone,
    String? role,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Создает модель из JSON
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'].toString(),
      email: json['email'] as String,
      phone: json['phone'] as String?,
      role: json['role'] as String? ?? 'user',
      createdAt: DateTime.parse(json['created_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'phone': phone,
      'role': role,
      'created_at': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }
}
