import 'package:equatable/equatable.dart';

/// Модель токенов аутентификации
class AuthTokens extends Equatable {
  /// Токен доступа
  final String accessToken;
  
  /// Тип токена (обычно 'bearer')
  final String tokenType;
  
  /// Срок действия токена в секундах
  final int? expiresIn;

  /// Создает модель токенов аутентификации
  const AuthTokens({
    required this.accessToken,
    required this.tokenType,
    this.expiresIn,
  });

  @override
  List<Object?> get props => [accessToken, tokenType, expiresIn];

  /// Создает копию модели с новыми значениями
  AuthTokens copyWith({
    String? accessToken,
    String? tokenType,
    int? expiresIn,
  }) {
    return AuthTokens(
      accessToken: accessToken ?? this.accessToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
    );
  }

  /// Создает модель из JSON
  /// 
  /// Формат ответа нового бэкенда:
  /// {
  ///   "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  ///   "token_type": "bearer",
  ///   "expires_in": 3600
  /// }
  factory AuthTokens.fromJson(Map<String, dynamic> json) {
    return AuthTokens(
      accessToken: json['access_token'] as String,
      tokenType: json['token_type'] as String,
      expiresIn: json['expires_in'] != null ? json['expires_in'] as int : null,
    );
  }

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    final map = {
      'access_token': accessToken,
      'token_type': tokenType,
    };
    
    if (expiresIn != null) {
      map['expires_in'] = expiresIn as dynamic;
    }
    
    return map;
  }
}
