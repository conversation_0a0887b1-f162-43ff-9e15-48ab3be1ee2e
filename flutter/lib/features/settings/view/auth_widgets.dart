import 'package:flutter/material.dart';
import 'package:luxury_app/shared/widgets/app_buttons.dart';

/// Универсальное поле ввода email
class EmailInputField extends StatelessWidget {
  const EmailInputField({
    super.key,
    required this.controller,
    this.onFieldSubmitted,
    this.validator,
  });
  final TextEditingController controller;
  final VoidCallback? onFieldSubmitted;
  final String? Function(String?)? validator;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: const InputDecoration(
        labelText: 'Email',
        prefixIcon: Icon(Icons.email),
      ),
      keyboardType: TextInputType.emailAddress,
      autofillHints: const [AutofillHints.email],
      validator: validator,
      onFieldSubmitted: (_) => onFieldSubmitted?.call(),
    );
  }
}

/// Универсальное поле ввода пароля
class PasswordInputField extends StatelessWidget {
  const PasswordInputField({
    super.key,
    required this.controller,
    this.onFieldSubmitted,
    this.minLength = 1,
    this.validator,
  });
  final TextEditingController controller;
  final VoidCallback? onFieldSubmitted;
  final int minLength;
  final String? Function(String?)? validator;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: const InputDecoration(
        labelText: 'Пароль',
        prefixIcon: Icon(Icons.lock),
      ),
      obscureText: true,
      autofillHints: const [AutofillHints.password],
      validator: validator,
      onFieldSubmitted: (_) => onFieldSubmitted?.call(),
    );
  }
}

/// Универсальное поле ввода телефона
class PhoneInputField extends StatelessWidget {
  const PhoneInputField({
    super.key,
    required this.controller,
    this.onFieldSubmitted,
    this.validator,
  });
  final TextEditingController controller;
  final VoidCallback? onFieldSubmitted;
  final String? Function(String?)? validator;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: const InputDecoration(
        labelText: 'Телефон (необязательно)',
        prefixIcon: Icon(Icons.phone),
      ),
      keyboardType: TextInputType.phone,
      validator: validator,
      onFieldSubmitted: (_) => onFieldSubmitted?.call(),
    );
  }
}

/// Универсальная кнопка для форм
class AuthFormButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isPrimary;
  final IconData? icon;
  const AuthFormButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isPrimary = true,
    this.icon,
  });
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: AppButton(
        text: text,
        onPressed: onPressed,
        icon: icon,
        style: isPrimary ? AppButtonStyle.primary : AppButtonStyle.outline,
        size: AppButtonSize.medium,
      ),
    );
  }
}

/// Информационный текст формы аутентификации
class AuthFormInfoText extends StatelessWidget {
  final String text;
  const AuthFormInfoText({super.key, required this.text});
  @override
  Widget build(BuildContext context) {
    return Text(text, style: const TextStyle(fontSize: 14));
  }
}
