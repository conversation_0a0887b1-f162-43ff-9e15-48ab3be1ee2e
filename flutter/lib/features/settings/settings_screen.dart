import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/features/settings/auth/auth_provider.dart';
import 'package:luxury_app/features/settings/auth/validators.dart';
import 'package:luxury_app/features/settings/cache_info_widget.dart';
import 'package:luxury_app/features/settings/theme_selector.dart';
import 'package:luxury_app/features/settings/view/auth_widgets.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/widgets/app_buttons.dart';

/// Экран настроек приложения
class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  // Контроллеры для авторизации
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _repeatPasswordController =
      TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  // Ключи форм
  final _loginFormKey = GlobalKey<FormState>();
  final _registerFormKey = GlobalKey<FormState>();

  // Флаг отображения формы регистрации
  bool _showRegisterForm = false;

  @override
  void initState() {
    super.initState();
    // Проверяем статус авторизации при необходимости
    // AuthNotifier автоматически проверяет статус при инициализации
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Настройки',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      ),
      body: ListView(
        padding: EdgeInsets.all(AppSizes.paddingM),
        children: [
          Center(child: const ThemeSelector()),
          SizedBox(height: AppSizes.paddingL),
          _buildAuthSection(),
          SizedBox(height: AppSizes.paddingL),
          Card(
            elevation: 2,
            surfaceTintColor: Theme.of(context).colorScheme.surface,
            child: Padding(
              padding: EdgeInsets.all(AppSizes.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Данные приложения',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const CacheInfoWidget(),
                ],
              ),
            ),
          ),
          SizedBox(height: AppSizes.paddingM),
        ],
      ),
    );
  }

  // Секция авторизации
  Widget _buildAuthSection() {
    return Consumer(
      builder: (context, ref, child) {
        final authState = ref.watch(authProvider);

        // Слушаем изменения состояния для очистки формы
        ref.listen<AuthState>(authProvider, (previous, next) {
          if (next.isAuthenticated && next.user != null) {
            _clearFormFields();
            setState(() {
              _showRegisterForm = false;
            });
          }
        });

        return Card(
          elevation: 4,
          surfaceTintColor: Theme.of(context).colorScheme.surface,
          child: Padding(
            padding: EdgeInsets.all(AppSizes.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),

                if (authState.isAuthenticated && authState.user != null)
                  _buildUserInfo(authState)
                else if (authState.isLoading)
                  const Center(child: CircularProgressIndicator())
                else
                  _showRegisterForm ? _buildRegisterForm() : _buildLoginForm(),
              ],
            ),
          ),
        );
      },
    );
  }

  // Информация о пользователе и кнопка выхода
  Widget _buildUserInfo(AuthState authState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: const Icon(Icons.person),
          title: Text(authState.user!.email),
        ),
        if (authState.user!.phone != null)
          Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: Text('Телефон: ${authState.user!.phone}'),
          ),
        const SizedBox(height: 16),
        Consumer(
          builder: (context, ref, child) {
            return AppButton(
              text: 'Выйти',
              onPressed: () {
                ref.read(authProvider.notifier).logout();
              },
              icon: LucideIcons.logOut,
              style: AppButtonStyle.secondary,
            );
          },
        ),
      ],
    );
  }

  // Форма входа - UI компонент для отображения формы авторизации
  // Presentation Layer: Widget
  Widget _buildLoginForm() {
    return Form(
      key: _loginFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Текстовое описание - можно вынести в константы или локализацию
          const AuthFormInfoText(
            text: 'Для использования ИИ-ассистентов необходимо войти',
          ),
          const SizedBox(height: 16),
          EmailInputField(
            controller: _emailController,
            onFieldSubmitted: _handleLogin,
            validator: null,
            key: const Key('login_email'),
          ),
          const SizedBox(height: 8),
          PasswordInputField(
            controller: _passwordController,
            onFieldSubmitted: _handleLogin,
            validator: (value) => validatePassword(value, minLength: 1),
            key: const Key('login_password'),
          ),
          const SizedBox(height: 20),
          AuthFormButton(text: 'Войти', onPressed: _handleLogin),
          const SizedBox(height: 8),
          AuthFormButton(
            text: 'Создать аккаунт',
            onPressed: _switchToRegisterMode,
            isPrimary: false,
          ),
        ],
      ),
    );
  }

  // Форма регистрации - UI компонент для отображения формы регистрации
  // Presentation Layer: Widget
  Widget _buildRegisterForm() {
    return Form(
      key: _registerFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const AuthFormInfoText(
            text: 'Создайте аккаунт для доступа к ИИ-ассистентам',
          ),
          const SizedBox(height: 8),
          const _RegisterPasswordHint(),
          const SizedBox(height: 8),
          EmailInputField(
            controller: _emailController,
            onFieldSubmitted: _handleRegister,
            validator: null,
            key: const Key('register_email'),
          ),
          const SizedBox(height: 8),
          PasswordInputField(
            controller: _passwordController,
            onFieldSubmitted: _handleRegister,
            minLength: 8,
            validator: (value) => validatePassword(value, minLength: 8),
            key: const Key('register_password'),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _repeatPasswordController,
            obscureText: true,
            autofillHints: const [AutofillHints.password],
            decoration: const InputDecoration(
              labelText: 'Повторите пароль',
              prefixIcon: Icon(Icons.lock_outline),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Повторите пароль';
              }
              if (value != _passwordController.text) {
                return 'Пароли не совпадают';
              }
              if (value.length < 8) {
                return 'Пароль должен быть не короче 8 символов';
              }
              return null;
            },
            onFieldSubmitted: (_) => _handleRegister(),
          ),
          const SizedBox(height: 8),
          PhoneInputField(
            controller: _phoneController,
            onFieldSubmitted: _handleRegister,
            validator: validatePhone,
            key: const Key('register_phone'),
          ),
          const SizedBox(height: 20),
          AuthFormButton(
            text: 'Зарегистрироваться',
            onPressed: _handleRegister,
          ),
          const SizedBox(height: 8),
          AuthFormButton(
            text: 'Уже есть аккаунт',
            onPressed: _switchToLoginMode,
            isPrimary: false,
          ),
        ],
      ),
    );
  }

  // Вспомогательный метод для очистки полей формы
  // Presentation Layer: UILogic
  void _clearFormFields() {
    _emailController.clear();
    _passwordController.clear();
    _repeatPasswordController.clear();
    _phoneController.clear();
  }

  // Вспомогательный метод для обработки входа
  // Presentation Layer: UILogic
  void _handleLogin() {
    if (_loginFormKey.currentState!.validate()) {
      ref
          .read(authProvider.notifier)
          .login(_emailController.text, _passwordController.text);
    }
  }

  // Вспомогательный метод для обработки регистрации
  // Presentation Layer: UILogic
  void _handleRegister() {
    if (_registerFormKey.currentState!.validate()) {
      ref
          .read(authProvider.notifier)
          .register(
            _emailController.text,
            _passwordController.text,
            phone:
                _phoneController.text.isNotEmpty ? _phoneController.text : null,
          );
    }
  }

  // Вспомогательный метод для переключения на режим регистрации
  // Presentation Layer: UILogic
  void _switchToRegisterMode() {
    setState(() {
      _showRegisterForm = true;
    });
  }

  // Вспомогательный метод для переключения на режим входа
  // Presentation Layer: UILogic
  void _switchToLoginMode() {
    setState(() {
      _showRegisterForm = false;
    });
  }
}

/// Приватный виджет для отображения подсказки о пароле при регистрации
class _RegisterPasswordHint extends StatelessWidget {
  const _RegisterPasswordHint();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          const Icon(Icons.info_outline, color: Colors.red, size: 18),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Пароль должен быть не менее 8 символов',
              style: TextStyle(
                color: Colors.red.shade700,
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
