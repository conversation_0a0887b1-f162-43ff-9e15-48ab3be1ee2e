import 'package:equatable/equatable.dart';

/// Состояние аудио плеера
class AudioState extends Equatable {
  final String? currentFileId;
  final String? audioUrl;
  final bool isPlaying;
  final bool isLoading;
  final bool isCompleted;
  final Duration position;
  final Duration duration;
  final bool showPlayer;
  final String? error;

  // Новые поля для глобального состояния
  final String? sourcePageId;
  final String? sourcePageTitle;
  final bool isMinimized;

  const AudioState({
    this.currentFileId,
    this.audioUrl,
    this.isPlaying = false,
    this.isLoading = false,
    this.isCompleted = false,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.showPlayer = false,
    this.error,
    this.sourcePageId,
    this.sourcePageTitle,
    this.isMinimized = false,
  });

  /// Создает копию с новыми значениями
  AudioState copyWith({
    String? currentFileId,
    String? audioUrl,
    bool? isPlaying,
    bool? isLoading,
    bool? isCompleted,
    Duration? position,
    Duration? duration,
    bool? showPlayer,
    String? error,
    String? sourcePageId,
    String? sourcePageTitle,
    bool? isMinimized,
  }) {
    return AudioState(
      currentFileId: currentFileId ?? this.currentFileId,
      audioUrl: audioUrl ?? this.audioUrl,
      isPlaying: isPlaying ?? this.isPlaying,
      isLoading: isLoading ?? this.isLoading,
      isCompleted: isCompleted ?? this.isCompleted,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      showPlayer: showPlayer ?? this.showPlayer,
      error: error ?? this.error,
      sourcePageId: sourcePageId ?? this.sourcePageId,
      sourcePageTitle: sourcePageTitle ?? this.sourcePageTitle,
      isMinimized: isMinimized ?? this.isMinimized,
    );
  }

  @override
  List<Object?> get props => [
    currentFileId,
    audioUrl,
    isPlaying,
    isLoading,
    isCompleted,
    position,
    duration,
    showPlayer,
    error,
    sourcePageId,
    sourcePageTitle,
    isMinimized,
  ];
}
