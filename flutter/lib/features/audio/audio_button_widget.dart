import 'package:flutter/material.dart';

/// Кнопка для воспроизведения аудио
/// Используется в режиме просмотра вики-статьи
class AudioButtonWidget extends StatelessWidget {
  /// ID файла для воспроизведения аудио
  final String fileId;

  /// Размер кнопки
  final double size;

  /// Отступы от края
  final double padding;

  /// Callback, вызываемый при нажатии на кнопку
  final VoidCallback? onPressed;

  const AudioButtonWidget({
    super.key,
    required this.fileId,
    this.size = 32.0,
    this.padding = 8.0,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = colorScheme.brightness == Brightness.dark;

    // Цвет тени, зависящий от темы
    final shadowColor = colorScheme.shadow.withAlpha(
      isDark ? (0.15 * 255).round() : (0.1 * 255).round(),
    );

    // Добавляем малую обводку для лучшего вида в темной теме
    final borderColor =
        isDark
            ? Theme.of(
              context,
            ).colorScheme.onSurface.withAlpha((0.2 * 255).round())
            : Colors.transparent;

    return Container(
      height: size,
      width: size,
      decoration: BoxDecoration(
        color: colorScheme.surface,
        shape: BoxShape.circle,
        border: Border.all(color: borderColor, width: 0.5),
        boxShadow: [
          BoxShadow(color: shadowColor, blurRadius: 6.0, spreadRadius: 1.0),
        ],
      ),
      child: IconButton(
        icon: const Icon(Icons.volume_up),
        onPressed: onPressed,
        tooltip: 'Слушать',
        padding: EdgeInsets.zero,
        iconSize: 16.0,
      ),
    );
  }
}
