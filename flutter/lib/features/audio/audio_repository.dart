import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:luxury_app/core/services/cache_service.dart';
import '../wiki/wiki_data/wiki_repository.dart';

/// Репозиторий для централизованной работы с аудиофайлами Wiki
class AudioRepository {
  final CacheService _cacheService;
  final WikiRepository _wikiRepository;

  AudioRepository(this._cacheService, this._wikiRepository);

  /// Получить путь к аудиофайлу для плеера (нативные платформы) или байты (web)
  Future<AudioSourceResult> getAudioSource({
    required String fileId,
    required String audioUrl,
  }) async {
    if (kIsWeb) {
      // Для web — просто скачиваем байты
      final bytes = await _wikiRepository.getAudioData(audioUrl, fileId);
      return AudioSourceResult.web(bytes: bytes);
    } else {
      // Для нативных платформ — кэшируем файл
      final hasCache = await _cacheService.hasAudio(fileId);
      if (hasCache) {
        final file = await _cacheService.getAudioFile(fileId);
        if (file != null) {
          return AudioSourceResult.native(path: file.path);
        } else {
          return AudioSourceResult.native(path: null);
        }
      } else {
        final bytes = await _wikiRepository.getAudioData(audioUrl, fileId);
        final file = await _cacheService.saveAudio(fileId, bytes);
        if (file != null) {
          return AudioSourceResult.native(path: file.path);
        } else {
          return AudioSourceResult.native(path: null);
        }
      }
    }
  }
}

/// Результат для источника аудио
class AudioSourceResult {
  final String? path;
  final Uint8List? bytes;
  final bool isWeb;

  AudioSourceResult.native({required this.path}) : bytes = null, isWeb = false;
  AudioSourceResult.web({required this.bytes}) : path = null, isWeb = true;
}
