import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/core/services/simple_audio_service.dart';
import 'package:luxury_app/features/audio/audio_bloc/audio_state.dart';

/// Notifier для управления аудио состоянием (адаптер для SimpleAudioService)
class AudioNotifier extends StateNotifier<AudioState> {
  late StreamSubscription<SimpleAudioState> _simpleAudioSubscription;

  AudioNotifier() : super(const AudioState()) {
    _initializeSimpleAudioListener();
  }

  /// Инициализация слушателя упрощенного аудио-сервиса
  void _initializeSimpleAudioListener() {
    // Подписываемся на изменения глобального состояния
    _simpleAudioSubscription = SimpleAudioService.instance.stateStream.listen((
      simpleState,
    ) {
      if (mounted) {
        // Преобразуем SimpleAudioState в AudioState
        state = AudioState(
          currentFileId: simpleState.currentFileId,
          audioUrl: simpleState.audioUrl,
          isPlaying: simpleState.isPlaying,
          isLoading: simpleState.isLoading,
          isCompleted: simpleState.isCompleted,
          position: simpleState.position,
          duration: simpleState.duration,
          showPlayer: simpleState.showPlayer,
          error: simpleState.error,
          sourcePageId: simpleState.sourcePageId,
          sourcePageTitle: simpleState.sourcePageTitle,
          isMinimized: simpleState.isMinimized,
        );
      }
    });

    // Устанавливаем начальное состояние
    final initialState = SimpleAudioService.instance.currentState;
    state = AudioState(
      currentFileId: initialState.currentFileId,
      audioUrl: initialState.audioUrl,
      isPlaying: initialState.isPlaying,
      isLoading: initialState.isLoading,
      isCompleted: initialState.isCompleted,
      position: initialState.position,
      duration: initialState.duration,
      showPlayer: initialState.showPlayer,
      error: initialState.error,
      sourcePageId: initialState.sourcePageId,
      sourcePageTitle: initialState.sourcePageTitle,
      isMinimized: initialState.isMinimized,
    );
  }

  /// Воспроизведение аудио (делегируем в SimpleAudioService)
  Future<void> playAudio({
    required String fileId,
    String? audioUrl,
    String? sourcePageId,
    String? sourcePageTitle,
    bool forceRefresh = false,
  }) async {
    await SimpleAudioService.instance.playAudio(
      fileId: fileId,
      audioUrl: audioUrl,
      sourcePageId: sourcePageId ?? fileId,
      sourcePageTitle: sourcePageTitle,
      forceRefresh: forceRefresh,
    );
  }

  /// Пауза аудио
  Future<void> pauseAudio() async {
    await SimpleAudioService.instance.pause();
  }

  /// Остановка аудио
  Future<void> stopAudio() async {
    await SimpleAudioService.instance.stop();
  }

  /// Поиск позиции в аудио
  Future<void> seekAudio(Duration position) async {
    await SimpleAudioService.instance.seek(position);
  }

  /// Перемотка назад
  Future<void> seekBackward(int seconds) async {
    await SimpleAudioService.instance.rewind();
  }

  /// Перемотка вперед
  Future<void> seekForward(int seconds) async {
    await SimpleAudioService.instance.fastForward();
  }

  /// Минимизация плеера
  void minimizePlayer() {
    SimpleAudioService.instance.minimizePlayer();
  }

  /// Максимизация плеера
  void maximizePlayer() {
    SimpleAudioService.instance.maximizePlayer();
  }

  /// Запуск аудио файла (альтернативный метод для совместимости)
  Future<void> startAudioFile(String fileId, String audioUrl) async {
    await playAudio(fileId: fileId, audioUrl: audioUrl);
  }

  /// Освобождение ресурсов
  @override
  void dispose() {
    _simpleAudioSubscription.cancel();
    super.dispose();
  }
}

/// Основной провайдер для аудио состояния
final audioProvider = StateNotifierProvider<AudioNotifier, AudioState>((ref) {
  return AudioNotifier();
});
