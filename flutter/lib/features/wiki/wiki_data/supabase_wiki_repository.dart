import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../../core/services/cache_service.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/utils/audio_utils.dart' as audio_utils;
import 'wiki_models.dart';
import 'wiki_repository.dart';

/// Реализация WikiRepository с использованием Supabase
class SupabaseWikiRepository implements WikiRepository {
  final SupabaseService _supabaseService;
  final CacheService _cacheService;
  final Dio _dio; // Для загрузки аудио файлов

  SupabaseWikiRepository(this._supabaseService, this._cacheService)
    : _dio = Dio();

  @override
  Future<List<WikiFolder>> getFolderHierarchy({
    bool forceRefresh = false,
  }) async {
    try {
      // Проверяем кэш если не требуется принудительное обновление
      if (!forceRefresh) {
        final cachedHierarchy = await _getCachedHierarchy();
        if (cachedHierarchy != null) {
          if (kDebugMode) {
            log(
              '📱 Wiki иерархия загружена из кэша: ${cachedHierarchy.length} папок',
            );
          }
          return cachedHierarchy;
        }
      }

      if (kDebugMode) {
        log('🌐 Загружаем Wiki иерархию из Supabase...');
      }

      // Загружаем папки из Supabase
      final foldersResponse = await _supabaseService
          .from('wiki_folders')
          .select('id, name, parent_id, sort_order, icon_0, icon_1, created_at')
          .order('sort_order');

      // Загружаем файлы из Supabase
      final filesResponse = await _supabaseService
          .from('wiki_files')
          .select('id, name, folder_id, sort_order, created_at, audio_url')
          .order('sort_order');

      // Строим иерархию
      final hierarchy = _buildFolderHierarchy(foldersResponse, filesResponse);

      // Кэшируем данные
      await _cacheHierarchy(hierarchy);

      if (kDebugMode) {
        log('✅ Загружена Wiki иерархия из Supabase');
      }

      return hierarchy;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки Wiki иерархии из Supabase: $e');
      }

      // При ошибке ВСЕГДА возвращаем кэшированные данные если есть
      final cachedHierarchy = await _getCachedHierarchy();
      if (cachedHierarchy != null) {
        if (kDebugMode) {
          log('📱 Возвращаем кэшированную Wiki иерархию из-за ошибки');
        }
        return cachedHierarchy;
      }

      // Если даже кэша нет, возвращаем пустой список вместо ошибки
      if (kDebugMode) {
        log('📱 Возвращаем пустую Wiki иерархию - нет данных');
      }
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> getFileContent(
    String fileId, {
    bool forceRefresh = false,
  }) async {
    final cacheKey = 'supabase_wiki_file_$fileId';

    try {
      // Проверяем кэш если не требуется принудительное обновление
      if (!forceRefresh) {
        final cachedContent = await _getCachedFileContent(cacheKey);
        if (cachedContent != null) {
          if (kDebugMode) {
            log('📱 Содержимое файла $fileId загружено из кэша');
          }
          return cachedContent;
        }
      }

      if (kDebugMode) {
        log('🌐 Загружаем содержимое файла $fileId из Supabase...');
      }

      // Загружаем файл из Supabase
      final response =
          await _supabaseService
              .from('wiki_files')
              .select('markdown, audio_url')
              .eq('id', fileId)
              .single();

      final fileContent = {
        'markdown': response['markdown'] ?? '',
        'audio_url': response['audio_url'],
      };

      // Кэшируем данные
      await _cacheFileContent(cacheKey, fileContent);

      if (kDebugMode) {
        log('✅ Загружено содержимое файла $fileId');
      }

      return fileContent;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки содержимого файла $fileId: $e');
      }

      // При ошибке ВСЕГДА возвращаем кэшированные данные если есть
      final cachedContent = await _getCachedFileContent(cacheKey);
      if (cachedContent != null) {
        if (kDebugMode) {
          log(
            '📱 Возвращаем кэшированное содержимое файла $fileId из-за ошибки',
          );
        }
        return cachedContent;
      }

      // Если даже кэша нет, возвращаем пустой контент вместо ошибки
      if (kDebugMode) {
        log('📱 Возвращаем пустое содержимое файла $fileId - нет данных');
      }
      return {
        'markdown':
            '# Файл недоступен\n\nСодержимое файла недоступно в режиме offline.',
        'audio_url': null,
      };
    }
  }

  @override
  Future<dynamic> getAudioData(String audioUrl, String fileName) async {
    try {
      if (kDebugMode) {
        log('🎵 Загружаем аудио файл: $audioUrl');
      }

      final fullUrl = audio_utils.getFullAudioUrl(audioUrl);

      // Используем Dio для загрузки аудио файлов
      final response = await _dio.get(
        fullUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      final audioData = response.data as List<int>;

      if (kDebugMode) {
        log('✅ Загружен аудио файл размером ${audioData.length} байт');
      }

      return audioData;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки аудио файла $audioUrl: $e');
      }
      rethrow;
    }
  }

  @override
  Future<List<WikiFolder>> searchWiki(String query) async {
    try {
      if (query.isEmpty) {
        return await getFolderHierarchy();
      }

      if (kDebugMode) {
        log('🔍 Поиск в Wiki: "$query"');
      }

      // Получаем полную иерархию
      final folders = await getFolderHierarchy();

      // Приводим запрос к нижнему регистру
      final lowercaseQuery = query.toLowerCase();

      // Фильтруем иерархию
      final filteredHierarchy = _filterFoldersByQuery(folders, lowercaseQuery);

      if (kDebugMode) {
        log('🔍 Найдено папок: ${filteredHierarchy.length}');
      }

      return filteredHierarchy;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка поиска в Wiki: $e');
      }
      rethrow;
    }
  }

  /// Подписка на Realtime обновления папок
  @override
  Stream<List<WikiFolder>> subscribeToFolderHierarchy() {
    return _supabaseService
        .from('wiki_folders')
        .stream(primaryKey: ['id'])
        .asyncMap((_) async {
          // При изменении папок перезагружаем всю иерархию
          final hierarchy = await getFolderHierarchy(forceRefresh: true);

          if (kDebugMode) {
            log('🔄 Realtime обновление Wiki иерархии');
          }

          return hierarchy;
        });
  }

  /// Подписка на Realtime обновления файлов
  @override
  Stream<List<WikiFolder>> subscribeToFileUpdates() {
    return _supabaseService
        .from('wiki_files')
        .stream(primaryKey: ['id'])
        .asyncMap((_) async {
          // При изменении файлов перезагружаем всю иерархию
          final hierarchy = await getFolderHierarchy(forceRefresh: true);

          if (kDebugMode) {
            log('🔄 Realtime обновление Wiki файлов');
          }

          return hierarchy;
        });
  }

  /// Строит иерархию папок из плоских данных
  List<WikiFolder> _buildFolderHierarchy(List foldersData, List filesData) {
    // Конвертируем файлы в WikiFile
    final files =
        filesData
            .map(
              (data) => WikiFile.fromJson({
                ...data,
                'has_tts': data['audio_url'] != null, // Вычисляем has_tts
              }),
            )
            .toList();

    // Группируем файлы по folder_id
    final Map<String, List<WikiFile>> filesByFolderId = {};
    for (final file in files) {
      filesByFolderId.putIfAbsent(file.folderId, () => []).add(file);
    }

    // Конвертируем папки в WikiFolder и добавляем файлы
    final Map<String, WikiFolder> folderMap = {};
    for (final data in foldersData) {
      final folderId = data['id'] as String;
      final folderFiles = filesByFolderId[folderId] ?? [];

      // Сортируем файлы по sort_order
      folderFiles.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

      folderMap[folderId] = WikiFolder.fromJson({
        ...data,
        'files': folderFiles.map((f) => f.toJson()).toList(),
        'subfolders': [], // Будем заполнять позже
      });
    }

    // Строим иерархию - добавляем подпапки
    final List<WikiFolder> rootFolders = [];

    for (final folder in folderMap.values) {
      if (folder.parentId == null) {
        // Корневая папка
        rootFolders.add(_buildFolderWithSubfolders(folder, folderMap));
      }
    }

    // Сортируем корневые папки
    rootFolders.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

    return rootFolders;
  }

  /// Рекурсивно строит папку с подпапками
  WikiFolder _buildFolderWithSubfolders(
    WikiFolder folder,
    Map<String, WikiFolder> folderMap,
  ) {
    final subfolders =
        folderMap.values
            .where((f) => f.parentId == folder.id)
            .map((f) => _buildFolderWithSubfolders(f, folderMap))
            .toList();

    subfolders.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

    return folder.copyWith(subfolders: subfolders);
  }

  /// Фильтрует иерархию папок по запросу
  List<WikiFolder> _filterFoldersByQuery(
    List<WikiFolder> folders,
    String query,
  ) {
    final Set<String> matchingFileIds = <String>{};
    _findMatchingFilesInHierarchy(folders, query, matchingFileIds);

    if (matchingFileIds.isEmpty) {
      return [];
    }

    return _createFilteredHierarchy(folders, matchingFileIds);
  }

  /// Рекурсивно ищет файлы, соответствующие запросу
  void _findMatchingFilesInHierarchy(
    List<WikiFolder> folders,
    String query,
    Set<String> matchingFileIds,
  ) {
    for (final folder in folders) {
      // Проверяем файлы в текущей папке
      for (final file in folder.files) {
        if (file.name.toLowerCase().contains(query)) {
          matchingFileIds.add(file.id);
        }
      }

      // Рекурсивно проверяем подпапки
      _findMatchingFilesInHierarchy(folder.subfolders, query, matchingFileIds);
    }
  }

  /// Создает отфильтрованную копию иерархии папок
  List<WikiFolder> _createFilteredHierarchy(
    List<WikiFolder> folders,
    Set<String> matchingFileIds,
  ) {
    final result = <WikiFolder>[];

    for (final folder in folders) {
      // Фильтруем файлы в текущей папке
      final filteredFiles =
          folder.files
              .where((file) => matchingFileIds.contains(file.id))
              .toList();

      // Рекурсивно фильтруем подпапки
      final filteredSubfolders = _createFilteredHierarchy(
        folder.subfolders,
        matchingFileIds,
      );

      // Добавляем папку в результат, только если в ней есть файлы или подпапки
      if (filteredFiles.isNotEmpty || filteredSubfolders.isNotEmpty) {
        result.add(
          folder.copyWith(files: filteredFiles, subfolders: filteredSubfolders),
        );
      }
    }

    return result;
  }

  /// Кэширование методы
  Future<List<WikiFolder>?> _getCachedHierarchy() async {
    try {
      final cachedData = _cacheService.getWikiHierarchy();
      if (cachedData != null) {
        final List<dynamic> jsonList = jsonDecode(cachedData);
        return jsonList
            .map((json) => WikiFolder.fromJson(json as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка чтения кэша Wiki иерархии: $e');
      }
    }

    return null;
  }

  Future<void> _cacheHierarchy(List<WikiFolder> hierarchy) async {
    try {
      final jsonList = hierarchy.map((folder) => folder.toJson()).toList();
      final jsonString = jsonEncode(jsonList);
      await _cacheService.saveWikiHierarchy(jsonString);
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка кэширования Wiki иерархии: $e');
      }
    }
  }

  Future<Map<String, dynamic>?> _getCachedFileContent(String cacheKey) async {
    try {
      final fileId = cacheKey.replaceFirst('supabase_wiki_file_', '');
      final cachedData = _cacheService.getWikiFileContent(fileId);
      if (cachedData != null) {
        return jsonDecode(cachedData) as Map<String, dynamic>;
      }
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка чтения кэша файла: $e');
      }
    }

    return null;
  }

  Future<void> _cacheFileContent(
    String cacheKey,
    Map<String, dynamic> content,
  ) async {
    try {
      final fileId = cacheKey.replaceFirst('supabase_wiki_file_', '');
      final jsonString = jsonEncode(content);
      await _cacheService.saveWikiFileContent(fileId, jsonString);
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка кэширования файла: $e');
      }
    }
  }
}
