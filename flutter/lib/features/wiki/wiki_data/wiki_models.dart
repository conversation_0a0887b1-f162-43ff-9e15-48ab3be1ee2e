/// Модели данных для работы с API wiki
library;

/// Модель для представления папки в wiki
class WikiFolder {
  final String id;
  final String name;
  final String? parentId;
  final int sortOrder;
  final String? icon0;
  final String? icon1;
  final DateTime? createdAt;
  final List<WikiFolder> subfolders;
  final List<WikiFile> files;

  const WikiFolder({
    required this.id,
    required this.name,
    this.parentId,
    required this.sortOrder,
    this.icon0,
    this.icon1,
    this.createdAt,
    this.subfolders = const [],
    this.files = const [],
  });

  factory WikiFolder.fromJson(Map<String, dynamic> json) {
    return WikiFolder(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      parentId: json['parent_id'],
      sortOrder: json['sort_order'] ?? 0,
      icon0: json['icon_0'],
      icon1: json['icon_1'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      subfolders:
          (json['subfolders'] as List?)
              ?.map((e) => WikiFolder.fromJson(Map<String, dynamic>.from(e)))
              .toList() ??
          [],
      files:
          (json['files'] as List?)
              ?.map((e) => WikiFile.fromJson(Map<String, dynamic>.from(e)))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'parent_id': parentId,
      'sort_order': sortOrder,
      'icon_0': icon0,
      'icon_1': icon1,
      'created_at': createdAt?.toIso8601String(),
      'subfolders': subfolders.map((folder) => folder.toJson()).toList(),
      'files': files.map((file) => file.toJson()).toList(),
    };
  }

  /// Создает копию папки с новыми подпапками и файлами
  WikiFolder copyWith({
    String? id,
    String? name,
    String? parentId,
    int? sortOrder,
    String? icon0,
    String? icon1,
    DateTime? createdAt,
    List<WikiFolder>? subfolders,
    List<WikiFile>? files,
  }) {
    return WikiFolder(
      id: id ?? this.id,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      sortOrder: sortOrder ?? this.sortOrder,
      icon0: icon0 ?? this.icon0,
      icon1: icon1 ?? this.icon1,
      createdAt: createdAt ?? this.createdAt,
      subfolders: subfolders ?? this.subfolders,
      files: files ?? this.files,
    );
  }
}

/// Модель для представления файла в wiki
class WikiFile {
  final String id;
  final String name;
  final String folderId;
  final int sortOrder;
  final DateTime? createdAt;
  final bool hasTts;
  final String? audioUrl; // URL для аудиофайла

  const WikiFile({
    required this.id,
    required this.name,
    required this.folderId,
    required this.sortOrder,
    this.createdAt,
    this.hasTts = false,
    this.audioUrl,
  });

  factory WikiFile.fromJson(Map<String, dynamic> json) {
    return WikiFile(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      folderId: json['folder_id'] ?? json['parent_id'] ?? '',
      sortOrder: json['sort_order'] ?? 0,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      hasTts: json['has_tts'] ?? false,
      audioUrl: json['audio_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'folder_id': folderId,
      'sort_order': sortOrder,
      'created_at': createdAt?.toIso8601String(),
      'has_tts': hasTts,
      'audio_url': audioUrl,
    };
  }

  /// Создает копию файла с обновленными полями
  WikiFile copyWith({
    String? id,
    String? name,
    String? folderId,
    int? sortOrder,
    DateTime? createdAt,
    bool? hasTts,
    String? audioUrl,
  }) {
    return WikiFile(
      id: id ?? this.id,
      name: name ?? this.name,
      folderId: folderId ?? this.folderId,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      hasTts: hasTts ?? this.hasTts,
      audioUrl: audioUrl ?? this.audioUrl,
    );
  }
}

