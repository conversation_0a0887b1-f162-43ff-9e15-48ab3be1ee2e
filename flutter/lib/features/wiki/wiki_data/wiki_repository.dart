import 'package:luxury_app/features/wiki/wiki_data/wiki_models.dart';

/// Интерфейс репозитория для работы с Wiki
abstract class WikiRepository {
  /// Получает иерархию папок и файлов
  Future<List<WikiFolder>> getFolderHierarchy({bool forceRefresh = false});

  /// Получает содержимое файла по ID (markdown и audioUrl)
  Future<Map<String, dynamic>> getFileContent(
    String fileId, {
    bool forceRefresh = false,
  });

  /// Получает данные аудиофайла (Uint8List для веб, String path для натива)
  /// по URL и желаемому имени файла для кэша.
  Future<dynamic> getAudioData(String audioUrl, String fileName);

  /// Выполняет поиск по Wiki и возвращает отфильтрованную иерархию папок
  Future<List<WikiFolder>> searchWiki(String query);

  /// Подписка на real-time обновления иерархии папок
  Stream<List<WikiFolder>> subscribeToFolderHierarchy();

  /// Подписка на real-time обновления файлов
  Stream<List<WikiFolder>> subscribeToFileUpdates();
}
