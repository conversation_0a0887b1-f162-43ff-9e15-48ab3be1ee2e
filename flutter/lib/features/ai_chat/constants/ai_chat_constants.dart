/// Константы для AI чата
class AIChatConstants {
  // Streaming markdown
  static const int streamingMarkdownDebounceMs = 100;

  // SSE обработка
  static const String sseDataPrefix = 'data: ';
  static const int sseDataPrefixLength = 6;

  // Кэш
  static const int maxCachedMessages = 100;
  static const String chatsListCacheKey = 'chats_list';

  /// Генерирует ключ кэша для сообщений чата
  static String getChatMessagesCacheKey(int chatId) => 'chat_messages_$chatId';
}
