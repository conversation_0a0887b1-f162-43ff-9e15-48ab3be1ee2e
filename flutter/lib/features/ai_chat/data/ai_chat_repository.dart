import 'package:luxury_app/features/ai_chat/data/ai_chat_model.dart';
import 'package:luxury_app/features/ai_chat/data/ai_message.dart';

/// Интерфейс репозитории для работы с чатами ИИ
abstract class AIChatRepository {
  /// Получает список чатов пользователя
  Future<List<AIChat>> getChats({
    int skip = 0,
    int limit = 100,
    bool forceRefresh = false,
  });

  /// Получает информацию о чате и его сообщениях
  Future<AIChat> getChatById(int chatId);

  /// Создает новый чат
  Future<AIChat> createChat(AIChatCreate chatCreate);

  /// Обновляет информацию о чате
  Future<AIChat> updateChat(int chatId, AIChatUpdate chatUpdate);

  /// Удаляет чат
  Future<void> deleteChat(int chatId);

  /// Получает список сообщений чата
  Future<List<AIMessage>> getChatMessages(
    int chatId, {
    int skip = 0,
    int limit = 100,
    bool forceRefresh = false,
  });

  /// Отправляет сообщение в чат и получает потоковый ответ от ИИ
  Stream<String> sendMessageToChatStream(
    int chatId,
    AISendMessageRequest request,
  );

  /// Подписка на real-time обновления чатов
  Stream<List<AIChat>> subscribeToChats();

  /// Подписка на real-time обновления сообщений чата
  Stream<List<AIMessage>> subscribeToMessages(int chatId);
}
