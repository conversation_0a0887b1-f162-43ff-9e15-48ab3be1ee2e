import 'dart:typed_data';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

/// Модель вложения в сообщении чата с ИИ
class AIAttachment extends Equatable {
  /// Тип вложения (image, file, audio)
  final String type;

  /// URL к файлу
  final String url;

  /// Имя файла
  final String? filename;

  /// MIME-тип
  final String? mimeType;

  /// Размер в байтах
  final int? size;

  /// Ширина (для изображений)
  final int? width;

  /// Высота (для изображений)
  final int? height;

  /// Локальные байты для превью (если файл еще не загружен или для оптимистичного UI)
  final Uint8List? localBytes;

  /// ID вложения
  final int id;

  /// ID сообщения
  final int messageId;

  /// Дата создания
  final DateTime createdAt;

  /// Создает модель вложения
  const AIAttachment({
    required this.type,
    required this.url,
    this.filename,
    this.mimeType,
    this.size,
    this.width,
    this.height,
    this.localBytes, // Добавлено
    required this.id,
    required this.messageId,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [
    type,
    url,
    filename,
    mimeType,
    size,
    width,
    height,
    localBytes,
    id,
    messageId,
    createdAt,
  ];

  /// Создает копию модели с новыми значениями
  AIAttachment copyWith({
    String? type,
    String? url,
    String? filename,
    String? mimeType,
    int? size,
    int? width,
    int? height,
    Uint8List? localBytes,
    int? id,
    int? messageId,
    DateTime? createdAt,
  }) {
    return AIAttachment(
      type: type ?? this.type,
      url: url ?? this.url,
      filename: filename ?? this.filename,
      mimeType: mimeType ?? this.mimeType,
      size: size ?? this.size,
      width: width ?? this.width,
      height: height ?? this.height,
      localBytes: localBytes ?? this.localBytes,
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Создает модель из JSON
  factory AIAttachment.fromJson(Map<String, dynamic> json) {
    return AIAttachment(
      type: json['type'] as String? ?? 'unknown',
      url: json['url'] as String? ?? '',
      filename: json['filename'] as String?,
      mimeType: json['mime_type'] as String?,
      size: json['size'] != null ? json['size'] as int : null,
      width: json['width'] != null ? json['width'] as int : null,
      height: json['height'] != null ? json['height'] as int : null,
      // localBytes не сериализуются в/из JSON, они только для локального использования
      id: json['id'] != null ? json['id'] as int : 0,
      messageId: json['message_id'] != null ? json['message_id'] as int : 0,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'] as String)
              : DateTime.now(),
    );
  }

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'url': url,
      if (filename != null) 'filename': filename,
      if (mimeType != null) 'mime_type': mimeType,
      if (size != null) 'size': size,
      if (width != null) 'width': width,
      if (height != null) 'height': height,
      // localBytes не сериализуются в JSON
      'id': id,
      'message_id': messageId,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// Модель для создания нового вложения
class AIAttachmentCreate {
  /// Тип вложения (image, file, audio)
  final String type;

  /// URL к файлу
  final String url;

  /// Имя файла
  final String? filename;

  /// MIME-тип
  final String? mimeType;

  /// Размер в байтах
  final int? size;

  /// Ширина (для изображений)
  final int? width;

  /// Высота (для изображений)
  final int? height;

  /// Локальные байты для превью
  final Uint8List? localBytes;

  /// Создает модель для создания вложения
  const AIAttachmentCreate({
    required this.type,
    required this.url,
    this.filename,
    this.mimeType,
    this.size,
    this.width,
    this.height,
    this.localBytes,
  });

  /// Создает копию модели с новыми значениями
  AIAttachmentCreate copyWith({
    String? type,
    String? url,
    String? filename,
    String? mimeType,
    int? size,
    int? width,
    int? height,
    Uint8List? localBytes,
  }) {
    return AIAttachmentCreate(
      type: type ?? this.type,
      url: url ?? this.url,
      filename: filename ?? this.filename,
      mimeType: mimeType ?? this.mimeType,
      size: size ?? this.size,
      width: width ?? this.width,
      height: height ?? this.height,
      localBytes: localBytes ?? this.localBytes,
    );
  }

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {'type': type, 'url': url};
    if (filename != null) data['filename'] = filename;
    if (mimeType != null) data['mime_type'] = mimeType;
    if (size != null) data['size'] = size;
    if (width != null) data['width'] = width;
    if (height != null) data['height'] = height;
    // localBytes не отправляются на сервер в JSON, они используются для MultipartFile

    return data;
  }
}
