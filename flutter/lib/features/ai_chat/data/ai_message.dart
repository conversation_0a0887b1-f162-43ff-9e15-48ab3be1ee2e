import 'package:equatable/equatable.dart';
import 'package:luxury_app/features/ai_chat/data/ai_attachment.dart';

/// Модель сообщения в чате с ИИ
class AIMessage extends Equatable {
  /// Уникальный идентификатор сообщения
  final int id;

  /// Роль отправителя сообщения ('user', 'assistant' или 'system')
  final String role;

  /// Содержимое сообщения
  final String? content;

  /// ID чата, к которому относится сообщение
  final int chatId;

  /// Время создания сообщения
  final DateTime createdAt;

  /// Список вложений в сообщении
  final List<AIAttachment> attachments;

  /// Метаданные сообщения
  final Map<String, dynamic>? metadata;

  /// Создает модель сообщения в чате с ИИ
  const AIMessage({
    required this.id,
    required this.role,
    this.content,
    required this.chatId,
    required this.createdAt,
    this.attachments = const [],
    this.metadata,
  });

  @override
  List<Object?> get props => [
    id,
    role,
    content,
    chatId,
    createdAt,
    attachments,
    metadata,
  ];

  /// Создает копию модели с новыми значениями
  AIMessage copyWith({
    int? id,
    String? role,
    String? content,
    int? chatId,
    DateTime? createdAt,
    List<AIAttachment>? attachments,
    Map<String, dynamic>? metadata,
  }) {
    return AIMessage(
      id: id ?? this.id,
      role: role ?? this.role,
      content: content ?? this.content,
      chatId: chatId ?? this.chatId,
      createdAt: createdAt ?? this.createdAt,
      attachments: attachments ?? this.attachments,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Создает модель из JSON
  factory AIMessage.fromJson(Map<String, dynamic> json) {
    final messageId = json['id'] != null ? json['id'] as int : 0;
    final metadata = json['metadata'] as Map<String, dynamic>?;

    return AIMessage(
      id: messageId,
      role: json['role'] as String? ?? 'unknown',
      content: json['content'] as String?,
      chatId: json['chat_id'] != null ? json['chat_id'] as int : 0,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'] as String)
              : DateTime.now(),
      attachments:
          json['attachments'] != null
              ? (json['attachments'] as List<dynamic>).map((e) {
                final attachmentJson = e as Map<String, dynamic>;
                // Добавляем messageId в JSON attachment'а
                attachmentJson['message_id'] = messageId;
                return AIAttachment.fromJson(attachmentJson);
              }).toList()
              : [],
      metadata: metadata,
    );
  }

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'role': role,
      if (content != null) 'content': content,
      'chat_id': chatId,
      'created_at': createdAt.toIso8601String(),
      'attachments': attachments.map((e) => e.toJson()).toList(),
      if (metadata != null) 'metadata': metadata,
    };
  }
}

/// Модель для создания нового сообщения
class AIMessageCreate {
  /// Роль отправителя сообщения ('user', 'assistant' или 'system')
  final String role;

  /// Содержимое сообщения
  final String? content;

  /// Список вложений в сообщении
  final List<AIAttachmentCreate>? attachments;

  /// Создает модель для создания сообщения
  const AIMessageCreate({required this.role, this.content, this.attachments});

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {'role': role};
    if (content != null) data['content'] = content;
    if (attachments != null) {
      data['attachments'] = attachments!.map((e) => e.toJson()).toList();
    }
    return data;
  }
}

/// Модель для отправки сообщения в чат ИИ
class AISendMessageRequest {
  /// Содержимое сообщения
  final String? content;

  /// Список вложений (для создания)
  final List<AIAttachmentCreate>? attachments;

  /// Включить потоковую передачу ответа
  final bool stream;

  /// Создает модель для отправки сообщения
  const AISendMessageRequest({
    this.content,
    this.attachments,
    this.stream = true,
  });

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (content != null) data['content'] = content;
    data['stream'] = stream;
    data['attachments'] = attachments?.map((e) => e.toJson()).toList() ?? [];
    return data;
  }
}
