import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';

import '../../../core/services/cache_service.dart';
import '../../../core/services/openrouter_service.dart';
import '../../../core/services/supabase_service.dart';
import '../constants/ai_chat_constants.dart';
import 'ai_attachment.dart';
import 'ai_chat_model.dart';
import 'ai_chat_repository.dart';
import 'ai_message.dart';

/// Реализация AIChatRepository с использованием Supabase
class SupabaseChatRepository implements AIChatRepository {
  final SupabaseService _supabaseService;
  final CacheService _cacheService;
  final OpenRouterService _openRouterService;

  SupabaseChatRepository(
    this._supabaseService,
    this._cacheService,
    this._openRouterService,
  );

  @override
  Future<List<AIChat>> getChats({
    int skip = 0,
    int limit = 100,
    bool forceRefresh = false,
  }) async {
    try {
      // Проверяем кэш если не требуется принудительное обновление
      if (!forceRefresh) {
        final cachedChats = await _getCachedChats();
        if (cachedChats != null && cachedChats.isNotEmpty) {
          if (kDebugMode) {
            log('📱 Загружено ${cachedChats.length} чатов из кэша');
          }
          return cachedChats;
        }
      }

      if (kDebugMode) {
        log('🌐 Загружаем чаты из Supabase...');
      }

      // Проверяем авторизацию
      if (!_supabaseService.isAuthenticated) {
        if (kDebugMode) {
          log('⚠️ Пользователь не авторизован, возвращаем кэшированные чаты');
        }
        final cachedChats = await _getCachedChats();
        return cachedChats ?? [];
      }

      final userId = _supabaseService.currentUserId!;

      // Загружаем чаты из Supabase
      final response = await _supabaseService
          .from('chats')
          .select('id, title, user_id, created_at, updated_at')
          .eq('user_id', userId)
          .order('updated_at', ascending: false)
          .range(skip, skip + limit - 1);

      final chats =
          (response as List).map((json) => _convertToAIChat(json)).toList();

      // Кэшируем данные
      await _cacheChats(chats);

      if (kDebugMode) {
        log('✅ Загружено ${chats.length} чатов из Supabase');
      }

      return chats;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки чатов из Supabase: $e');
      }

      // При ошибке ВСЕГДА возвращаем кэшированные данные
      final cachedChats = await _getCachedChats();
      if (kDebugMode) {
        log(
          '📱 Возвращаем ${cachedChats?.length ?? 0} кэшированных чатов из-за ошибки',
        );
      }
      return cachedChats ?? [];
    }
  }

  @override
  Future<AIChat> getChatById(int chatId) async {
    try {
      if (kDebugMode) {
        log('🌐 Загружаем чат $chatId из Supabase...');
      }

      // Проверяем авторизацию
      if (!_supabaseService.isAuthenticated) {
        throw Exception('Пользователь не авторизован');
      }

      // Загружаем чат с сообщениями
      final response =
          await _supabaseService
              .from('chats')
              .select('''
            id, title, user_id, created_at, updated_at,
            messages:messages(
              id, chat_id, role, content, metadata, created_at,
              attachments:attachments(
                id, message_id, type, url, filename, mime_type, size, width, height, metadata, created_at
              )
            )
          ''')
              .eq('id', chatId)
              .single();

      final chat = _convertToAIChatWithMessages(response);

      if (kDebugMode) {
        log(
          '✅ Загружен чат $chatId с ${chat.messages?.length ?? 0} сообщениями',
        );
      }

      return chat;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки чата $chatId: $e');
      }
      rethrow;
    }
  }

  @override
  Future<AIChat> createChat(AIChatCreate chatCreate) async {
    try {
      if (kDebugMode) {
        log('🌐 Создаем новый чат: "${chatCreate.title}"');
      }

      // Проверяем авторизацию
      if (!_supabaseService.isAuthenticated) {
        throw Exception('Пользователь не авторизован');
      }

      final userId = _supabaseService.currentUserId!;

      try {
        // Пытаемся создать чат в Supabase
        final response =
            await _supabaseService
                .from('chats')
                .insert({'title': chatCreate.title, 'user_id': userId})
                .select('id, title, user_id, created_at, updated_at')
                .single();

        final newChat = _convertToAIChat(response);

        // Инвалидируем кэш чатов
        await _invalidateChatsCache();

        if (kDebugMode) {
          log('✅ Создан новый чат с ID: ${newChat.id}');
        }

        return newChat;
      } catch (networkError) {
        if (kDebugMode) {
          log(
            '⚠️ Ошибка сети при создании чата, создаем локально: $networkError',
          );
        }

        // Создаем локальный чат с временным ID
        final now = DateTime.now();
        final localChat = AIChat(
          id:
              -DateTime.now()
                  .millisecondsSinceEpoch, // Отрицательный ID для локальных чатов
          title: chatCreate.title,
          userId: userId,
          createdAt: now,
          updatedAt: now,
        );

        // Сохраняем в локальный кэш
        await _addChatToCache(localChat);

        if (kDebugMode) {
          log('✅ Создан локальный чат с временным ID: ${localChat.id}');
        }

        return localChat;
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка создания чата: $e');
      }
      rethrow;
    }
  }

  @override
  Future<AIChat> updateChat(int chatId, AIChatUpdate chatUpdate) async {
    try {
      if (kDebugMode) {
        log('🌐 Обновляем чат $chatId');
      }

      // Проверяем авторизацию
      if (!_supabaseService.isAuthenticated) {
        throw Exception('Пользователь не авторизован');
      }

      // Подготавливаем данные для обновления
      final updateData = <String, dynamic>{};
      if (chatUpdate.title != null) {
        updateData['title'] = chatUpdate.title;
      }

      if (updateData.isEmpty) {
        throw Exception('Нет данных для обновления');
      }

      // Обновляем чат в Supabase
      final response =
          await _supabaseService
              .from('chats')
              .update(updateData)
              .eq('id', chatId)
              .select('id, title, user_id, created_at, updated_at')
              .single();

      final updatedChat = _convertToAIChat(response);

      // Инвалидируем кэш
      await _invalidateChatsCache();

      if (kDebugMode) {
        log('✅ Обновлен чат $chatId');
      }

      return updatedChat;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка обновления чата $chatId: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> deleteChat(int chatId) async {
    try {
      if (kDebugMode) {
        log('🌐 Удаляем чат $chatId');
      }

      // Проверяем авторизацию
      if (!_supabaseService.isAuthenticated) {
        throw Exception('Пользователь не авторизован');
      }

      // Удаляем чат из Supabase (каскадно удалятся сообщения и вложения)
      await _supabaseService.from('chats').delete().eq('id', chatId);

      // Инвалидируем кэш
      await _invalidateChatsCache();
      await _invalidateChatMessagesCache(chatId);

      if (kDebugMode) {
        log('✅ Удален чат $chatId');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка удаления чата $chatId: $e');
      }
      rethrow;
    }
  }

  @override
  Future<List<AIMessage>> getChatMessages(
    int chatId, {
    int skip = 0,
    int limit = 100,
    bool forceRefresh = false,
  }) async {
    final cacheKey = _getChatMessagesCacheKey(chatId);

    try {
      // Проверяем кэш если не требуется принудительное обновление
      if (!forceRefresh) {
        final cachedMessages = await _getCachedMessages(cacheKey);
        if (cachedMessages != null && cachedMessages.isNotEmpty) {
          if (kDebugMode) {
            log(
              '📱 Загружено ${cachedMessages.length} сообщений чата $chatId из кэша',
            );
          }
          return cachedMessages;
        }
      }

      if (kDebugMode) {
        log('🌐 Загружаем сообщения чата $chatId из Supabase...');
      }

      // Загружаем сообщения из Supabase
      final response = await _supabaseService
          .from('messages')
          .select('id, chat_id, role, content, attachments, created_at')
          .eq('chat_id', chatId)
          .order('created_at', ascending: true)
          .range(skip, skip + limit - 1);

      final messages =
          (response as List).map((json) => _convertToAIMessage(json)).toList();

      // Кэшируем данные
      await _cacheMessages(cacheKey, messages);

      if (kDebugMode) {
        log(
          '✅ Загружено ${messages.length} сообщений чата $chatId из Supabase',
        );
      }

      return messages;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки сообщений чата $chatId: $e');
      }

      // При ошибке ВСЕГДА возвращаем кэшированные данные
      final cachedMessages = await _getCachedMessages(cacheKey);
      if (kDebugMode) {
        log(
          '📱 Возвращаем ${cachedMessages?.length ?? 0} кэшированных сообщений чата $chatId из-за ошибки',
        );
      }
      return cachedMessages ?? [];
    }
  }

  @override
  Stream<String> sendMessageToChatStream(
    int chatId,
    AISendMessageRequest request,
  ) async* {
    int? assistantMessageId;

    try {
      if (kDebugMode) {
        log('🤖 Начинаем потоковый AI ответ для чата $chatId');
      }

      // Сначала сохраняем пользовательское сообщение
      final userMessageData = <String, dynamic>{
        'chat_id': chatId,
        'role': 'user',
        'content': request.content,
        'created_at': DateTime.now().toIso8601String(),
      };

      await _supabaseService.from('messages').insert(userMessageData);

      // Создаем временное сообщение AI в БД для предотвращения потери
      final assistantMessageData = <String, dynamic>{
        'chat_id': chatId,
        'role': 'assistant',
        'content': '', // Начинаем с пустого контента
        'created_at': DateTime.now().toIso8601String(),
      };

      final assistantMessageResponse =
          await _supabaseService
              .from('messages')
              .insert(assistantMessageData)
              .select('id')
              .single();

      assistantMessageId = assistantMessageResponse['id'] as int;

      if (kDebugMode) {
        log('✅ Создано временное сообщение AI с ID: $assistantMessageId');
      }

      // Получаем историю сообщений для контекста (включая только что созданные)
      final messages = await getChatMessages(
        chatId,
        limit: 20,
        forceRefresh: true,
      );

      // Используем OpenRouter для генерации потокового ответа
      final openRouterService = _openRouterService;

      String accumulatedContent = '';
      int lastUpdateLength = 0;
      const int updateThreshold = 50; // Обновляем БД каждые 50 символов

      await for (final event in openRouterService.generateStreamingResponse(
        messages: messages,
        userMessage: request.content ?? '',
        attachments: request.attachments?.map((a) => a.toJson()).toList(),
      )) {
        // Преобразуем в формат, ожидаемый Flutter UI
        final eventData = jsonEncode(event);
        yield eventData;

        // Накапливаем контент и периодически сохраняем в БД
        if (event['type'] == 'completion' &&
            event['data']?['content'] != null) {
          accumulatedContent += event['data']['content'] as String;

          // Инкрементально обновляем БД для предотвращения потери данных
          if (accumulatedContent.length - lastUpdateLength >= updateThreshold) {
            try {
              await _supabaseService
                  .from('messages')
                  .update({'content': accumulatedContent})
                  .eq('id', assistantMessageId);

              lastUpdateLength = accumulatedContent.length;

              if (kDebugMode) {
                log(
                  '💾 Инкрементальное сохранение: ${accumulatedContent.length} символов',
                );
              }
            } catch (e) {
              if (kDebugMode) {
                log('⚠️ Ошибка инкрементального сохранения: $e');
              }
              // Продолжаем стриминг даже при ошибке сохранения
            }
          }
        }
      }

      // Финальное обновление сообщения AI в Supabase
      if (accumulatedContent.isNotEmpty) {
        try {
          await _supabaseService
              .from('messages')
              .update({'content': accumulatedContent})
              .eq('id', assistantMessageId);

          if (kDebugMode) {
            log(
              '✅ AI ответ финально сохранен в Supabase: ${accumulatedContent.length} символов',
            );
          }
        } catch (e) {
          if (kDebugMode) {
            log('❌ Ошибка финального сохранения AI ответа: $e');
          }
        }
      }

      // Инвалидируем кэш сообщений после сохранения
      await _invalidateChatMessagesCache(chatId);
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка потокового AI ответа: $e');
      }

      // Если произошла ошибка и у нас есть ID сообщения, удаляем его
      if (assistantMessageId != null) {
        try {
          await _supabaseService
              .from('messages')
              .delete()
              .eq('id', assistantMessageId);

          if (kDebugMode) {
            log('🗑️ Удалено неполное сообщение AI после ошибки');
          }
        } catch (deleteError) {
          if (kDebugMode) {
            log('⚠️ Ошибка удаления неполного сообщения: $deleteError');
          }
        }
      }

      yield jsonEncode({
        'type': 'error',
        'data': {
          'error': e.toString(),
          'message': 'Произошла ошибка при генерации AI ответа',
        },
      });
    }
  }

  /// Подписка на Realtime обновления чатов
  @override
  Stream<List<AIChat>> subscribeToChats() {
    return _supabaseService
        .from('chats')
        .stream(primaryKey: ['id'])
        .order('updated_at', ascending: false)
        .map((data) {
          final chats =
              (data as List).map((json) => _convertToAIChat(json)).toList();

          // Обновляем кэш при получении новых данных
          _cacheChats(chats);

          if (kDebugMode) {
            log('🔄 Realtime обновление чатов: ${chats.length} чатов');
          }

          return chats;
        });
  }

  /// Подписка на Realtime обновления сообщений чата
  @override
  Stream<List<AIMessage>> subscribeToMessages(int chatId) {
    return _supabaseService
        .from('messages')
        .stream(primaryKey: ['id'])
        .eq('chat_id', chatId)
        .order('created_at', ascending: true)
        .map((data) {
          final messages =
              (data as List).map((json) => _convertToAIMessage(json)).toList();

          // Обновляем кэш при получении новых данных
          final cacheKey = _getChatMessagesCacheKey(chatId);
          _cacheMessages(cacheKey, messages);

          if (kDebugMode) {
            log(
              '🔄 Realtime обновление сообщений чата $chatId: ${messages.length} сообщений',
            );
          }

          return messages;
        });
  }

  /// Конвертация данных Supabase в модели
  AIChat _convertToAIChat(Map<String, dynamic> json) {
    return AIChat(
      id: json['id'] as int,
      title: json['title'] as String,
      userId: json['user_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  AIChat _convertToAIChatWithMessages(Map<String, dynamic> json) {
    final messages = json['messages'] as List?;
    return AIChat(
      id: json['id'] as int,
      title: json['title'] as String,
      userId: json['user_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      messages:
          messages?.map((msgJson) => _convertToAIMessage(msgJson)).toList(),
    );
  }

  AIMessage _convertToAIMessage(Map<String, dynamic> json) {
    final attachments = json['attachments'] as List?;
    return AIMessage(
      id: json['id'] as int,
      role: json['role'] as String,
      content: json['content'] as String?,
      chatId: json['chat_id'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
      attachments:
          attachments
              ?.map((attJson) => _convertToAIAttachment(attJson))
              .toList() ??
          [],
    );
  }

  AIAttachment _convertToAIAttachment(Map<String, dynamic> json) {
    return AIAttachment(
      id: json['id'] as int,
      messageId: json['message_id'] as int,
      type: json['type'] as String,
      url: json['url'] as String,
      filename: json['filename'] as String?,
      mimeType: json['mime_type'] as String?,
      size: json['size'] as int?,
      width: json['width'] as int?,
      height: json['height'] as int?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// Кэширование методы
  String _getChatMessagesCacheKey(int chatId) =>
      AIChatConstants.getChatMessagesCacheKey(chatId);

  Future<List<AIChat>?> _getCachedChats() async {
    try {
      final cachedData = _cacheService.getChats();
      if (cachedData != null) {
        final List<dynamic> jsonList = jsonDecode(cachedData);
        return jsonList
            .map((json) => AIChat.fromJson(json as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка чтения кэша чатов: $e');
      }
    }

    return null;
  }

  Future<void> _cacheChats(List<AIChat> chats) async {
    try {
      final jsonList = chats.map((chat) => chat.toJson()).toList();
      final jsonString = jsonEncode(jsonList);
      await _cacheService.saveChats(jsonString);
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка кэширования чатов: $e');
      }
    }
  }

  /// Добавляет новый чат в кэш
  Future<void> _addChatToCache(AIChat newChat) async {
    try {
      // Получаем текущие чаты из кэша
      final cachedChats = await _getCachedChats() ?? <AIChat>[];

      // Добавляем новый чат в начало списка
      final updatedChats = [newChat, ...cachedChats];

      // Сохраняем обновленный список
      await _cacheChats(updatedChats);
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка добавления чата в кэш: $e');
      }
    }
  }

  Future<List<AIMessage>?> _getCachedMessages(String cacheKey) async {
    try {
      final chatId = int.parse(cacheKey.split('_').last);
      final cachedData = _cacheService.getChatMessages(chatId);
      if (cachedData != null) {
        final List<dynamic> jsonList = jsonDecode(cachedData);
        return jsonList
            .map((json) => AIMessage.fromJson(json as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка чтения кэша сообщений: $e');
      }
    }

    return null;
  }

  Future<void> _cacheMessages(String cacheKey, List<AIMessage> messages) async {
    try {
      final chatId = int.parse(cacheKey.split('_').last);
      final jsonList = messages.map((message) => message.toJson()).toList();
      final jsonString = jsonEncode(jsonList);
      await _cacheService.saveChatMessages(chatId, jsonString);
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка кэширования сообщений: $e');
      }
    }
  }

  Future<void> _invalidateChatsCache() async {
    try {
      await _cacheService.removeChatsList();
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка инвалидации кэша чатов: $e');
      }
    }
  }

  Future<void> _invalidateChatMessagesCache(int chatId) async {
    try {
      await _cacheService.removeChatMessages(chatId);
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка инвалидации кэша сообщений чата $chatId: $e');
      }
    }
  }
}
