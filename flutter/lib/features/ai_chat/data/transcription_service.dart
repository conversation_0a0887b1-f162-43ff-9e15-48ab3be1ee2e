import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';

/// Сервис для транскрибации аудио
class TranscriptionService with LoggerMixin {
  final Ref _ref;

  TranscriptionService(this._ref);

  /// Транскрибирует аудио файл
  Future<String> transcribeAudio(File audioFile) async {
    try {
      final openAIService = _ref.read(openAIServiceProvider);

      logInfo('🎤 Начинаем транскрибацию файла: ${audioFile.path}');

      // Проверяем размер файла
      final fileSize = await audioFile.length();
      if (fileSize > openAIService.maxFileSize) {
        throw Exception(
          'Файл слишком большой. Максимальный размер: ${openAIService.maxFileSize ~/ (1024 * 1024)} MB',
        );
      }

      // Проверяем формат файла
      final filename = audioFile.path.split('/').last;
      if (!openAIService.isSupportedAudioFormat(filename)) {
        throw Exception('Неподдерживаемый формат аудио файла');
      }

      // Транскрибируем
      final transcription = await openAIService.transcribeAudio(audioFile);

      logInfo('✅ Транскрибация завершена: ${transcription.length} символов');

      return transcription;
    } catch (e) {
      logError('❌ Ошибка транскрибации: $e');
      rethrow;
    }
  }

  /// Транскрибирует аудио из байтов
  Future<String> transcribeAudioFromBytes(
    List<int> audioBytes,
    String filename,
  ) async {
    try {
      logInfo('🎤 Начинаем транскрибацию из байтов: $filename');

      // Проверяем размер
      if (audioBytes.length > _ref.read(openAIServiceProvider).maxFileSize) {
        throw Exception(
          'Файл слишком большой. Максимальный размер: ${_ref.read(openAIServiceProvider).maxFileSize ~/ (1024 * 1024)} MB',
        );
      }

      // Проверяем формат
      if (!_ref.read(openAIServiceProvider).isSupportedAudioFormat(filename)) {
        throw Exception('Неподдерживаемый формат аудио файла');
      }

      // Транскрибируем
      final transcription = await _ref
          .read(openAIServiceProvider)
          .transcribeAudioFromBytes(audioBytes, filename);

      logInfo('✅ Транскрибация завершена: ${transcription.length} символов');

      return transcription;
    } catch (e) {
      logError('❌ Ошибка транскрибации: $e');
      rethrow;
    }
  }

  /// Поддерживаемые форматы аудио
  List<String> get supportedFormats => [
    'mp3',
    'mp4',
    'mpeg',
    'mpga',
    'm4a',
    'wav',
    'webm',
    'ogg',
    'flac',
    'aac',
  ];

  /// Максимальный размер файла в байтах
  int get maxFileSize => _ref.read(openAIServiceProvider).maxFileSize;
}
