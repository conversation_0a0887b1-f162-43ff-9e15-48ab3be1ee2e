import 'package:equatable/equatable.dart';
import 'package:luxury_app/features/ai_chat/data/ai_message.dart';

/// Модель чата с ИИ
class AIChat extends Equatable {
  /// Уникальный идентификатор чата
  final int id;

  /// Название чата
  final String title;

  /// ID пользователя, которому принадлежит чат
  final String userId;

  /// Время создания чата
  final DateTime createdAt;

  /// Время последнего обновления чата
  final DateTime updatedAt;

  /// Список сообщений в чате (может быть null, если сообщения не загружены)
  final List<AIMessage>? messages;

  /// Создает модель чата с ИИ
  const AIChat({
    required this.id,
    required this.title,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
    this.messages,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    userId,
    createdAt,
    updatedAt,
    messages,
  ];

  /// Создает копию модели с новыми значениями
  AIChat copyWith({
    int? id,
    String? title,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<AIMessage>? messages,
  }) {
    return AIChat(
      id: id ?? this.id,
      title: title ?? this.title,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      messages: messages ?? this.messages,
    );
  }

  /// Создает модель из JSON
  factory AIChat.fromJson(Map<String, dynamic> json) {
    return AIChat(
      id: json['id'] as int,
      title: json['title'] as String,
      userId: json['user_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      messages:
          json['messages'] != null
              ? (json['messages'] as List<dynamic>)
                  .map((e) => AIMessage.fromJson(e as Map<String, dynamic>))
                  .toList()
              : null,
    );
  }

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      if (messages != null)
        'messages': messages!.map((e) => e.toJson()).toList(),
    };
  }
}

/// Модель для создания нового чата
class AIChatCreate {
  /// Название чата
  final String title;

  /// Создает модель для создания чата
  const AIChatCreate({required this.title});

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    return {'title': title};
  }
}

/// Модель для обновления чата
class AIChatUpdate {
  /// Новое название чата
  final String? title;

  /// Создает модель для обновления чата
  const AIChatUpdate({this.title});

  /// Преобразует модель в JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (title != null) data['title'] = title;
    return data;
  }
}
