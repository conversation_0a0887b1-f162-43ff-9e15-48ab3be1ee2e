import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:luxury_app/core/services/cache_service.dart';
import 'package:luxury_app/core/services/connectivity_service.dart';
import 'package:luxury_app/features/ai_chat/data/ai_chat_model.dart';
import 'package:luxury_app/features/ai_chat/data/ai_chat_repository.dart';

/// Состояние списка чатов
class ChatListState {
  final List<AIChat> chats;
  final bool isLoading;
  final String? error;
  final bool isInitialized;

  const ChatListState({
    this.chats = const [],
    this.isLoading = false,
    this.error,
    this.isInitialized = false,
  });

  ChatListState copyWith({
    List<AIChat>? chats,
    bool? isLoading,
    String? error,
    bool? isInitialized,
  }) {
    return ChatListState(
      chats: chats ?? this.chats,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }

  bool get hasError => error != null;
}

/// Notifier для управления состоянием списка чатов
class ChatListNotifier extends StateNotifier<ChatListState> with LoggerMixin {
  final AIChatRepository _chatRepository;
  StreamSubscription<List<AIChat>>? _chatsSubscription;
  StreamSubscription<bool>? _connectivitySubscription;
  StreamSubscription<bool>? _cacheClearedSubscription;

  ChatListNotifier(this._chatRepository) : super(const ChatListState()) {
    _initializeChats();
    _subscribeToCacheCleared();
  }

  @override
  void dispose() {
    _chatsSubscription?.cancel();
    _connectivitySubscription?.cancel();
    _cacheClearedSubscription?.cancel();
    super.dispose();
  }

  /// Инициализация чатов
  Future<void> _initializeChats() async {
    logInfo('🔄 Инициализация списка чатов...');

    // Сначала загружаем из кэша
    await _loadFromCache();

    // Подписываемся на изменения состояния подключения
    _subscribeToConnectivity();
  }

  /// Загрузка данных из кэша
  Future<void> _loadFromCache() async {
    try {
      logInfo('📱 Загружаем чаты из кэша...');
      state = state.copyWith(isLoading: true, error: null);

      final chats = await _chatRepository.getChats();

      state = state.copyWith(
        chats: chats,
        isLoading: false,
        isInitialized: true,
      );

      logInfo('✅ Загружено ${chats.length} чатов из кэша');
    } catch (e) {
      logError('❌ Ошибка загрузки чатов из кэша: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Ошибка загрузки данных из кэша',
        isInitialized: true,
      );
    }
  }

  /// Подписка на изменения состояния подключения
  void _subscribeToConnectivity() {
    _connectivitySubscription = ConnectivityService
        .instance
        .supabaseStatusStream
        .listen((isConnected) {
          if (isConnected) {
            logInfo('🌐 Подключение восстановлено - обновляем чаты');
            // Принудительно обновляем данные при подключении
            loadChats();
          } else {
            logInfo('📱 Подключение потеряно - работаем с кэшем');
          }
        });

    // Подписываемся на real-time обновления
    _subscribeToChats();
  }

  /// Подписка на real-time обновления чатов
  void _subscribeToChats() {
    logInfo('🔄 Подписываемся на real-time обновления чатов...');

    try {
      _chatsSubscription = _chatRepository.subscribeToChats().listen(
        (chats) {
          logInfo('✅ Получено ${chats.length} чатов через real-time');
          state = state.copyWith(
            chats: chats,
            isLoading: false,
            isInitialized: true,
          );
        },
        onError: (error) {
          logError('❌ Ошибка real-time подписки на чаты: $error');
          // Не показываем ошибку пользователю - данные из кэша уже загружены
        },
      );
    } catch (e) {
      logError('❌ Ошибка создания подписки на чаты: $e');
      // Не показываем ошибку пользователю - данные из кэша уже загружены
    }
  }

  /// Подписка на очистку кэша
  void _subscribeToCacheCleared() {
    _cacheClearedSubscription = CacheService.instance.cacheClearedStream.listen(
      (_) {
        logInfo('🔄 Кэш очищен, сбрасываем состояние чатов');

        // Сбрасываем состояние до начального
        state = const ChatListState();

        // Перезагружаем данные
        _initializeChats();
      },
    );
  }

  /// Загружает список чатов (для совместимости и принудительного обновления)
  Future<void> loadChats() async {
    logInfo('🔄 Принудительная загрузка чатов...');
    state = state.copyWith(isLoading: true, error: null);

    try {
      final chats = await _chatRepository.getChats();

      state = state.copyWith(
        chats: chats,
        isLoading: false,
        isInitialized: true,
      );

      logInfo('✅ Загружено ${chats.length} чатов');
    } catch (e) {
      logError('❌ Ошибка загрузки чатов: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Ошибка загрузки данных',
        isInitialized: true,
      );
    }
  }

  /// Обновляет список чатов (для RefreshIndicator)
  Future<void> refreshChats() async {
    await loadChats();
  }

  /// Создает новый чат
  Future<AIChat?> createChat(String title) async {
    try {
      logInfo('🔄 Создаем новый чат: $title');
      final chat = await _chatRepository.createChat(AIChatCreate(title: title));
      logInfo('✅ Создан чат: ${chat.title}');
      return chat;
    } catch (e) {
      logError('❌ Ошибка создания чата: $e');
      return null;
    }
  }

  /// Переименовывает чат
  Future<void> renameChat(int chatId, String newTitle) async {
    logInfo('🔄 Переименовываем чат $chatId в $newTitle');

    try {
      await _chatRepository.updateChat(chatId, AIChatUpdate(title: newTitle));
      logInfo('✅ Чат переименован');
      // Real-time подписка автоматически обновит список
    } catch (e) {
      logError('❌ Ошибка переименования чата: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Автоматически обновляет название чата на основе первого сообщения пользователя
  /// Вызывается когда пользователь отправляет первое сообщение в чат с названием "Новый чат"
  Future<void> updateChatTitleFromFirstMessage(
    int chatId,
    String firstMessage,
  ) async {
    try {
      // Находим чат в текущем состоянии
      final chat = state.chats.firstWhere(
        (c) => c.id == chatId,
        orElse: () => throw Exception('Чат не найден'),
      );

      // Обновляем название только если это "Новый чат"
      if (chat.title == 'Новый чат') {
        // Обрезаем сообщение до 50 символов для названия
        String newTitle = firstMessage.trim();
        if (newTitle.length > 50) {
          newTitle = '${newTitle.substring(0, 50)}...';
        }

        logInfo(
          '🔄 Автоматически обновляем название чата $chatId на: $newTitle',
        );
        await renameChat(chatId, newTitle);
      }
    } catch (e) {
      logError('❌ Ошибка автоматического обновления названия чата: $e');
      // Не показываем ошибку пользователю, так как это не критично
    }
  }

  /// Удаляет чат
  Future<bool> deleteChat(int chatId) async {
    logInfo('🔄 Удаляем чат $chatId');

    try {
      await _chatRepository.deleteChat(chatId);
      logInfo('✅ Чат удален');
      return true;
    } catch (e) {
      logError('❌ Ошибка удаления чата: $e');
      return false;
    }
  }

  /// Обновляет чат в списке
  Future<void> updateChat(AIChat updatedChat) async {
    final updatedChats =
        state.chats.map((chat) {
          return chat.id == updatedChat.id ? updatedChat : chat;
        }).toList();

    state = state.copyWith(chats: updatedChats);
  }

  /// Очищает ошибки
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Провайдер для списка чатов
final chatListProvider = StateNotifierProvider<ChatListNotifier, ChatListState>(
  (ref) {
    return ChatListNotifier(ref.read(aiChatRepositoryProvider));
  },
);

/// Провайдер для фильтрованного списка чатов
final filteredChatListProvider = Provider.family<List<AIChat>, String>((
  ref,
  searchTerm,
) {
  final chatListState = ref.watch(chatListProvider);
  final chats = chatListState.chats;

  if (searchTerm.isEmpty) return chats;

  final lowerSearchTerm = searchTerm.toLowerCase();
  return chats
      .where((chat) => chat.title.toLowerCase().contains(lowerSearchTerm))
      .toList();
});
