import 'package:flutter/material.dart';
import 'package:luxury_app/features/ai_chat/data/ai_attachment.dart';
import 'package:luxury_app/features/ai_chat/data/ai_message.dart';
import 'package:luxury_app/shared/widgets/fullscreen_image_screen.dart';
import 'package:luxury_app/shared/widgets/markdown_renderer.dart';

/// ЕДИНЫЙ универсальный компонент для отображения сообщений ассистента
/// Показывает и стриминг, и финальные сообщения
class UnifiedAssistantMessage extends StatefulWidget {
  final AIMessage message;
  final bool isStreaming;

  const UnifiedAssistantMessage({
    super.key,
    required this.message,
    this.isStreaming = false,
  });

  @override
  State<UnifiedAssistantMessage> createState() =>
      _UnifiedAssistantMessageState();
}

class _UnifiedAssistantMessageState extends State<UnifiedAssistantMessage>
    with TickerProviderStateMixin {
  late AnimationController _cursorController;
  late Animation<double> _cursorAnimation;

  @override
  void initState() {
    super.initState();
    _cursorController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _cursorAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _cursorController, curve: Curves.easeInOut),
    );

    if (widget.isStreaming) {
      _cursorController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(UnifiedAssistantMessage oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isStreaming && !oldWidget.isStreaming) {
      _cursorController.repeat(reverse: true);
    } else if (!widget.isStreaming && oldWidget.isStreaming) {
      _cursorController.stop();
      _cursorController.reset();
    }
  }

  @override
  void dispose() {
    _cursorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildContent();
  }

  /// Строит основной контент сообщения
  Widget _buildContent() {
    final content = widget.message.content ?? '';
    final hasContent = content.isNotEmpty;
    final hasAttachments = widget.message.attachments.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Текст сообщения
        if (hasContent || widget.isStreaming) _buildTextWithCursor(content),

        // Разделитель между текстом и вложениями
        if (hasContent && hasAttachments) const SizedBox(height: 8),

        // Вложения
        if (hasAttachments) _buildAttachments(),
      ],
    );
  }

  /// Строит текст с курсором для стрима
  Widget _buildTextWithCursor(String content) {
    // Для стрима показываем курсор
    if (widget.isStreaming) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: MarkdownRenderer(data: content.isEmpty ? '' : content),
          ),
          AnimatedBuilder(
            animation: _cursorAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _cursorAnimation.value,
                child: Container(
                  width: 2,
                  height: 16,
                  margin: const EdgeInsets.only(left: 4, top: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              );
            },
          ),
        ],
      );
    }

    // Для обычного сообщения просто показываем текст
    return MarkdownRenderer(data: content);
  }

  /// Строит вложения сообщения
  Widget _buildAttachments() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          widget.message.attachments.map((attachment) {
            return _buildAttachmentPreview(attachment);
          }).toList(),
    );
  }

  /// Строит превью вложения
  Widget _buildAttachmentPreview(AIAttachment attachment) {
    if (attachment.type == 'image') {
      return _buildImageAttachment(attachment);
    } else {
      return _buildFileAttachment(attachment);
    }
  }

  /// Строит превью изображения
  Widget _buildImageAttachment(AIAttachment attachment) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) =>
                    FullscreenImageScreen(
                      imageUrl: attachment.url,
                      heroTag: 'assistant_attachment_${attachment.id}',
                    ),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
            transitionDuration: const Duration(milliseconds: 300),
            reverseTransitionDuration: const Duration(milliseconds: 300),
            opaque: false,
          ),
        );
      },
      child: Hero(
        tag: 'assistant_attachment_${attachment.id}',
        child: Container(
          width: 150,
          height: 150,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              attachment.url,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return const Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return const Center(child: Icon(Icons.broken_image, size: 40));
              },
            ),
          ),
        ),
      ),
    );
  }

  /// Строит превью файла
  Widget _buildFileAttachment(AIAttachment attachment) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getFileIcon(attachment.type),
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                attachment.filename ?? 'Файл',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              if (attachment.size != null)
                Text(
                  _formatFileSize(attachment.size!),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Получает иконку для типа файла
  IconData _getFileIcon(String type) {
    switch (type) {
      case 'image':
        return Icons.image;
      case 'video':
        return Icons.video_file;
      case 'audio':
        return Icons.audio_file;
      case 'document':
        return Icons.description;
      default:
        return Icons.attach_file;
    }
  }

  /// Форматирует размер файла
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
