import 'package:flutter/material.dart';
import 'package:luxury_app/features/ai_chat/data/ai_message.dart';

import 'unified_assistant_message.dart';
import 'user_message.dart';

class MessageItem extends StatelessWidget {
  final AIMessage message;
  final bool isStreaming;

  const MessageItem({
    super.key,
    required this.message,
    this.isStreaming = false,
  });

  @override
  Widget build(BuildContext context) {
    final isUser = message.role == 'user';
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        crossAxisAlignment:
            isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Используем единый компонент для всех случаев
          isUser
              ? UserMessage(message: message)
              : UnifiedAssistantMessage(
                message: message,
                isStreaming: isStreaming,
              ),
        ],
      ),
    );
  }
}
