import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:path_provider/path_provider.dart';

/// Виджет для записи и транскрибации аудио
class AudioRecorderWidget extends ConsumerStatefulWidget {
  /// Колбэк для обработки завершения транскрибации
  final void Function(String text) onTranscriptionComplete;

  /// Колбэк для обработки ошибок
  final void Function(String message) onError;

  /// Колбэк для отмены записи
  final VoidCallback? onCancel;

  const AudioRecorderWidget({
    super.key,
    required this.onTranscriptionComplete,
    required this.onError,
    this.onCancel,
  });

  @override
  ConsumerState<AudioRecorderWidget> createState() =>
      _AudioRecorderWidgetState();
}

class _AudioRecorderWidgetState extends ConsumerState<AudioRecorderWidget>
    with TickerProviderStateMixin, LoggerMixin {
  bool _isRecording = true;
  bool _isTranscribing = false;
  int _recordingSeconds = 0;

  // Анимация для записи
  late AnimationController _recordingAnimationController;
  late AnimationController _waveAnimationController;
  late Animation<double> _recordingPulseAnimation;

  // Данные для имитации волн
  List<double> _waveData = [];

  @override
  void initState() {
    super.initState();

    _recordingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _waveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _recordingPulseAnimation = Tween<double>(begin: 1.0, end: 1.5).animate(
      CurvedAnimation(
        parent: _recordingAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Сразу запускаем анимацию
    _recordingAnimationController.repeat(reverse: true);

    // Начинаем запись немедленно без задержки
    _startRecording();
  }

  @override
  void dispose() {
    _recordingAnimationController.dispose();
    _waveAnimationController.dispose();
    super.dispose();
  }

  Future<void> _startRecording() async {
    // Не вызываем setState здесь, так как _isRecording уже true
    _recordingSeconds = 0;
    _waveData = [];

    try {
      logInfo('Starting audio recording process');

      final audioRecordingService = ref.read(audioRecordingServiceProvider);

      // Устанавливаем колбэк для мониторинга амплитуды
      audioRecordingService.startAmplitudeMonitoring((amplitude, seconds) {
        if (mounted) {
          setState(() {
            _recordingSeconds = seconds;
            _waveData.add(amplitude);
            if (_waveData.length > 50) {
              _waveData.removeAt(0);
            }
          });
        }
      });

      final success = await audioRecordingService.startRecording();

      if (!success) {
        if (mounted) {
          // Если запись не удалась, сразу вызываем onError
          widget.onError('Не удалось начать запись аудио');
        }
        return;
      }

      logInfo('Audio recording started successfully');

      // Запись уже началась, интерфейс уже показан
    } catch (e, s) {
      logError('Error starting audio recording', e, s);
      if (mounted) {
        // При ошибке также вызываем onError
        widget.onError('Ошибка начала записи: ${e.toString()}');
      }
    }
  }

  Future<void> _stopRecording() async {
    setState(() {
      _isRecording = false;
      _isTranscribing = true;
    });

    _recordingAnimationController.stop();
    _recordingAnimationController.reset();

    try {
      final audioRecordingService = ref.read(audioRecordingServiceProvider);
      final recordingData = await audioRecordingService.stopRecording();

      if (recordingData != null) {
        // Создаем временный файл для транскрипции
        final tempDir = await getTemporaryDirectory();
        final tempFile = File(
          '${tempDir.path}/temp_audio.${recordingData.extension}',
        );
        await tempFile.writeAsBytes(recordingData.bytes);

        final transcriptionService = ref.read(transcriptionServiceProvider);
        final text = await transcriptionService.transcribeAudio(tempFile);

        if (!mounted) return;

        if (text.trim().isNotEmpty) {
          widget.onTranscriptionComplete(text);
        } else {
          widget.onError('Не удалось распознать речь в аудио');
        }

        // Удаляем временный файл
        try {
          await tempFile.delete();
        } catch (e) {
          logWarning('Failed to delete temporary audio file', e);
        }
      } else {
        widget.onError('Не удалось получить аудио данные');
      }
    } catch (e) {
      if (!mounted) return;
      widget.onError('Ошибка при обработке аудио: ${e.toString()}');
    }
  }

  void _cancelRecording() {
    _recordingAnimationController.stop();
    _recordingAnimationController.reset();

    if (_isRecording) {
      final audioRecordingService = ref.read(audioRecordingServiceProvider);
      audioRecordingService
          .stopRecording(); // Останавливаем запись без обработки
    }

    widget.onCancel?.call();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (_isTranscribing) {
      return _buildTranscribingInterface(colorScheme);
    } else if (_isRecording) {
      return _buildRecordingInterface(colorScheme);
    } else {
      // Не должно происходить, но на всякий случай
      return const SizedBox.shrink();
    }
  }

  Widget _buildRecordingInterface(ColorScheme colorScheme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(24),
      ),
      child: Row(
        children: [
          // Анимированная точка
          AnimatedBuilder(
            animation: _recordingPulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _recordingPulseAnimation.value,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              );
            },
          ),

          const SizedBox(width: 12),

          // Аудио волна
          Expanded(
            child: SizedBox(
              height: 24,
              child: _buildWaveAnimation(colorScheme),
            ),
          ),

          const SizedBox(width: 12),

          // Время
          Text(
            '${_recordingSeconds ~/ 60}:${(_recordingSeconds % 60).toString().padLeft(2, '0')}',
            style: TextStyle(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),

          const SizedBox(width: 12),

          // Кнопки
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: _stopRecording,
                icon: Icon(Icons.stop, color: colorScheme.primary, size: 20),
                padding: const EdgeInsets.all(4),
                tooltip: 'Остановить запись',
              ),
              IconButton(
                onPressed: _cancelRecording,
                icon: Icon(
                  Icons.close,
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 20,
                ),
                padding: const EdgeInsets.all(4),
                tooltip: 'Отменить запись',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWaveAnimation(ColorScheme colorScheme) {
    return AnimatedBuilder(
      animation: _waveAnimationController,
      builder: (context, child) {
        return CustomPaint(
          size: const Size(double.infinity, 24),
          painter: WaveformPainter(
            waveData: _waveData,
            waveColor: colorScheme.primary,
          ),
        );
      },
    );
  }

  Widget _buildTranscribingInterface(ColorScheme colorScheme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(24),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Распознавание речи...',
              style: TextStyle(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Кастомный painter для отображения аудио волны
class WaveformPainter extends CustomPainter {
  final List<double> waveData;
  final Color waveColor;

  WaveformPainter({required this.waveData, required this.waveColor});

  @override
  void paint(Canvas canvas, Size size) {
    if (waveData.isEmpty) return;

    final width = size.width;
    final height = size.height;
    final centerY = height / 2;
    final barWidth = width / waveData.length;

    // Рисуем вертикальные полоски (как классическая аудио-волна)
    for (int i = 0; i < waveData.length; i++) {
      final x = i * barWidth;
      final amplitude = waveData[i];

      // Высота полоски зависит от амплитуды
      final barHeight = amplitude * height * 0.8; // 80% от высоты

      // Цвет зависит от громкости
      Color barColor;
      if (amplitude < 0.2) {
        barColor = waveColor.withValues(alpha: 0.3); // тишина
      } else if (amplitude < 0.5) {
        barColor = waveColor.withValues(alpha: 0.6); // средний звук
      } else if (amplitude < 0.8) {
        barColor = waveColor.withValues(alpha: 0.8); // громкий звук
      } else {
        barColor = Colors.red.withValues(alpha: 0.9); // очень громко
      }

      final paint =
          Paint()
            ..color = barColor
            ..style = PaintingStyle.fill;

      // Рисуем полоску от центра вверх и вниз
      final rect = Rect.fromLTWH(
        x,
        centerY - barHeight / 2,
        barWidth * 0.8, // делаем промежутки между полосками
        barHeight,
      );

      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(1)),
        paint,
      );
    }

    // Добавляем центральную линию
    final centerLinePaint =
        Paint()
          ..color = waveColor.withValues(alpha: 0.2)
          ..strokeWidth = 1.0;

    canvas.drawLine(
      Offset(0, centerY),
      Offset(width, centerY),
      centerLinePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
