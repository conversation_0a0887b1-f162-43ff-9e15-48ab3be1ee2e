import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:luxury_app/features/ai_chat/data/ai_attachment.dart';
import 'package:luxury_app/features/ai_chat/data/ai_message.dart';
import 'package:luxury_app/shared/constants/api_constants.dart';
import 'package:luxury_app/shared/widgets/fullscreen_image_screen.dart';

class UserMessage extends StatelessWidget {
  const UserMessage({super.key, required this.message});
  final AIMessage message;

  @override
  Widget build(BuildContext context) {
    if (kDebugMode && message.attachments.isNotEmpty) {
      debugPrint(
        '👤 [UserMessage] Отображаем сообщение с ${message.attachments.length} вложениями',
      );
      for (int i = 0; i < message.attachments.length; i++) {
        final attachment = message.attachments[i];
        debugPrint(
          '📎 [UserMessage] Вложение $i: ${attachment.filename}, тип: ${attachment.type}, localBytes: ${attachment.localBytes != null ? "есть (${attachment.localBytes!.length} байт)" : "нет"}, URL: ${attachment.url}',
        );
      }
    }

    return Align(
      alignment: Alignment.centerRight,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Вложения
              if (message.attachments.isNotEmpty) ...[
                _buildAttachments(context),
                if (message.content != null && message.content!.isNotEmpty)
                  const SizedBox(height: 8),
              ],
              // Текст сообщения
              if (message.content != null && message.content!.isNotEmpty)
                SelectableText(
                  message.content!,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Строит блок вложений
  Widget _buildAttachments(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Изображения отображаем в сетке
        ...message.attachments.where((a) => a.type == 'image').map((
          attachment,
        ) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            constraints: const BoxConstraints(maxWidth: 200, maxHeight: 200),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _buildImage(context, attachment),
            ),
          );
        }),
        // Другие файлы отображаем как карточки
        ...message.attachments.where((a) => a.type != 'image').map((
          attachment,
        ) {
          return Container(
            margin: const EdgeInsets.only(bottom: 4),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getAttachmentIcon(attachment.type),
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    attachment.filename ?? 'Файл',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  /// Получает иконку для типа вложения
  IconData _getAttachmentIcon(String type) {
    switch (type) {
      case 'image':
        return Icons.image;
      case 'video':
        return Icons.videocam;
      case 'audio':
        return Icons.audiotrack;
      case 'document':
        return Icons.description;
      default:
        return Icons.attach_file;
    }
  }

  /// Открывает изображение в полноэкранном режиме
  void _openFullscreenImage(
    BuildContext context, {
    String? imageUrl,
    Uint8List? imageBytes,
    String? filename,
  }) {
    String heroTag =
        imageUrl ?? filename ?? 'user_image_${imageBytes.hashCode}';

    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) => FullscreenImageScreen(
              imageUrl: imageUrl,
              imageBytes: imageBytes,
              heroTag: heroTag,
            ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 300),
        reverseTransitionDuration: const Duration(milliseconds: 300),
        opaque: false,
      ),
    );
  }

  /// Строит виджет изображения
  Widget _buildImage(BuildContext context, AIAttachment attachment) {
    // Проверяем, есть ли локальные байты (для оптимистичных сообщений)
    if (attachment.localBytes != null) {
      return GestureDetector(
        onTap:
            () => _openFullscreenImage(
              context,
              imageBytes: attachment.localBytes,
              filename: attachment.filename,
            ),
        child: Hero(
          tag: 'user_image_${attachment.filename ?? attachment.id}',
          child: Image.memory(
            attachment.localBytes!,
            fit: BoxFit.cover,
            gaplessPlayback: true,
            errorBuilder: (context, error, stackTrace) {
              return _buildErrorPlaceholder();
            },
          ),
        ),
      );
    }

    // Если нет локальных байтов, используем URL
    if (attachment.url.isEmpty) {
      return _buildErrorPlaceholder();
    }

    // Определяем полный URL изображения
    String imageUrl = attachment.url;
    if (!imageUrl.startsWith('http')) {
      imageUrl = '${ApiConstants.baseImageUrl}$imageUrl';
    }

    return GestureDetector(
      onTap:
          () => _openFullscreenImage(
            context,
            imageUrl: imageUrl,
            filename: attachment.filename,
          ),
      child: Hero(
        tag: 'user_image_${attachment.filename ?? attachment.id}',
        child: Image.network(
          imageUrl,
          fit: BoxFit.cover,
          gaplessPlayback: true,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              width: 200,
              height: 200,
              color: Colors.grey[200],
              child: Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                  strokeWidth: 2,
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return _buildErrorPlaceholder();
          },
        ),
      ),
    );
  }

  /// Строит placeholder для ошибки загрузки
  Widget _buildErrorPlaceholder() {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.broken_image, size: 32, color: Colors.grey[600]),
          const SizedBox(height: 8),
          Text(
            'Ошибка загрузки',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}
