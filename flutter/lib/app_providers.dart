import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_initializer.dart';
import 'package:luxury_app/core/errors/error_notifier.dart';
import 'package:luxury_app/core/services/audio_recording_service.dart';
import 'package:luxury_app/core/services/background_sync_service.dart';
import 'package:luxury_app/core/services/cache_service.dart';
import 'package:luxury_app/core/services/connectivity_service.dart';
import 'package:luxury_app/core/services/file_processing_service.dart';
import 'package:luxury_app/core/services/openai_service.dart';
import 'package:luxury_app/core/services/openrouter_service.dart';
import 'package:luxury_app/core/services/supabase_service.dart';
import 'package:luxury_app/core/services/supabase_storage_service.dart';
import 'package:luxury_app/features/ai_chat/data/ai_chat_repository.dart';
import 'package:luxury_app/features/ai_chat/data/supabase_chat_repository.dart';
import 'package:luxury_app/features/ai_chat/data/transcription_service.dart';
import 'package:luxury_app/features/audio/audio_repository.dart';
import 'package:luxury_app/features/news/news_repository.dart';
import 'package:luxury_app/features/news/supabase_news_repository.dart';
import 'package:luxury_app/features/settings/auth/auth_repository/auth_cache_service.dart';
import 'package:luxury_app/features/settings/auth/auth_repository/auth_repository.dart';
import 'package:luxury_app/features/settings/auth/auth_repository/supabase_auth_repository.dart';
import 'package:luxury_app/features/wiki/wiki_data/supabase_wiki_repository.dart';
import 'package:luxury_app/features/wiki/wiki_data/wiki_repository.dart';

// === CORE SERVICES ===

/// Провайдер для SupabaseService
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService.instance;
});

/// Провайдер для ConnectivityService
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  return ConnectivityService.instance;
});

/// Провайдер для BackgroundSyncService
final backgroundSyncServiceProvider = Provider<BackgroundSyncService>((ref) {
  return BackgroundSyncService.instance;
});

/// Провайдер для CacheService
final cacheServiceProvider = Provider<CacheService>((ref) {
  return CacheService.instance;
});

/// Провайдер для AuthCacheService
final authCacheServiceProvider = Provider<AuthCacheService>((ref) {
  return AuthCacheService.instance;
});

/// Провайдер для ErrorNotifier
final errorNotifierProvider = Provider<ErrorNotifier>((ref) {
  return ErrorNotifier();
});

/// Провайдер для FileProcessingService
final fileProcessingServiceProvider = Provider<FileProcessingService>((ref) {
  return FileProcessingService();
});

/// Провайдер для OpenAIService
final openAIServiceProvider = Provider<OpenAIService>((ref) {
  return OpenAIService();
});

/// Провайдер для OpenRouterService
final openRouterServiceProvider = Provider<OpenRouterService>((ref) {
  return OpenRouterService();
});

/// Провайдер для AudioRecordingService
final audioRecordingServiceProvider = Provider<AudioRecordingService>((ref) {
  return AudioRecordingService();
});

/// Провайдер для SupabaseStorageService
final supabaseStorageServiceProvider = Provider<SupabaseStorageService>((ref) {
  return SupabaseStorageService(
    supabaseService: ref.read(supabaseServiceProvider),
    fileProcessingService: ref.read(fileProcessingServiceProvider),
  );
});

// === REPOSITORIES ===

/// Провайдер для AuthRepository
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return SupabaseAuthRepository(
    ref.read(supabaseServiceProvider),
    ref.read(authCacheServiceProvider),
    ref.read(cacheServiceProvider),
  );
});

/// Провайдер для NewsRepository
final newsRepositoryProvider = Provider<NewsRepository>((ref) {
  return SupabaseNewsRepository(
    ref.read(supabaseServiceProvider),
    ref.read(cacheServiceProvider),
  );
});

/// Провайдер для WikiRepository
final wikiRepositoryProvider = Provider<WikiRepository>((ref) {
  return SupabaseWikiRepository(
    ref.read(supabaseServiceProvider),
    ref.read(cacheServiceProvider),
  );
});

/// Провайдер для AIChatRepository
final aiChatRepositoryProvider = Provider<AIChatRepository>((ref) {
  return SupabaseChatRepository(
    ref.read(supabaseServiceProvider),
    ref.read(cacheServiceProvider),
    ref.read(openRouterServiceProvider),
  );
});

/// Провайдер для AudioRepository
final audioRepositoryProvider = Provider<AudioRepository>((ref) {
  return AudioRepository(
    ref.read(cacheServiceProvider),
    ref.read(wikiRepositoryProvider),
  );
});

/// Провайдер для TranscriptionService
final transcriptionServiceProvider = Provider<TranscriptionService>((ref) {
  return TranscriptionService(ref);
});

/// Провайдер для инициализации BackgroundSyncService
final backgroundSyncInitProvider = FutureProvider<BackgroundSyncService>((
  ref,
) async {
  final wikiRepository = ref.read(wikiRepositoryProvider);
  final chatRepository = ref.read(aiChatRepositoryProvider);
  final newsRepository = ref.read(newsRepositoryProvider);

  await AppInitializer.initBackgroundSync(
    wikiRepository: wikiRepository,
    chatRepository: chatRepository,
    newsRepository: newsRepository,
  );

  return BackgroundSyncService.instance;
});

// === HELPER PROVIDERS ===

/// Провайдер для проверки инициализации всех сервисов
final servicesInitializedProvider = Provider<bool>((ref) {
  // Проверяем, что все критические сервисы инициализированы
  try {
    // Читаем провайдеры для инициализации
    ref.read(supabaseServiceProvider);
    final cacheService = ref.read(cacheServiceProvider);

    // Проверяем реальную инициализацию сервисов
    return AppInitializer.isInitialized && cacheService.box != null;
  } catch (e) {
    return false;
  }
});
