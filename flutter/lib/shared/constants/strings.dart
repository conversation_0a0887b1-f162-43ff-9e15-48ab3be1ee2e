/// Содержит все текстовые константы приложения, сгруппированные по категориям
class AppText {
  // Приватный конструктор, чтобы запретить создание экземпляров
  const AppText._();

  // БРЕНД И ПРИЛОЖЕНИЕ
  // -------------------
  /// Полное название приложения
  static const String appTitle = 'LuxuryApp';
  static const String appTitlePrefix = 'luxury';
  static const String appTitleSuffix = 'App';

  // ЗАГОЛОВКИ ЭКРАНОВ И РАЗДЕЛОВ
  // -------------------
  static const String aiTitle = 'ИИ';
  static const String wikiTitle = 'Wiki';
  static const String settingsTitle = 'Настройки';

  // ПОДСКАЗКИ И ТУЛТИПЫ
  // -------------------
  static const String searchFieldLabel = 'Поиск...';
  static const String messageInputPlaceholder = 'Введите сообщение...';
  static const String copiedToClipboard = 'Текст скопирован';

  // НАВИГАЦИЯ
  // -------------------
  static const String drawerSettings = 'Настройки';
  static const String drawerNewChat = 'Новый чат';

  // О ПРИЛОЖЕНИИ
  // -------------------
  static const String appName = 'LuxuryApp';
  static const String appVersion = '0.5.0';
  static const String helpTooltip = 'Помощь и информация';
  static const String appLegalese = '© 2025 LuxuryApp. Все права защищены.';

  // ПОИСК И РЕЗУЛЬТАТЫ
  // -------------------
  static const String searchAction = 'Поиск';
  static const String searchRetry = 'Повторить';
  static const String searchNoResults = 'Ничего не найдено';
  static const String wikiEmptyState = 'Загрузка Wiki...';

  // ДЕЙСТВИЯ
  // -------------------
  static const String closeAction = 'Закрыть';
  static const String backAction = 'Назад';
  static const String newChatAction = 'Новый чат';
  static const String sendAction = 'Отправить';
  static const String copyAction = 'Копировать';
  static const String deleteAction = 'Удалить';
  static const String shareAction = 'Поделиться';
  static const String confirmAction = 'Подтвердить';
  static const String cancelAction = 'Отмена';

  // ОШИБКИ И УВЕДОМЛЕНИЯ
  // -------------------
  static const String loadingError = 'Ошибка загрузки';
  static const String serverError =
      'Ошибка загрузки с сервера. Показаны сохраненные данные.';
  static const String generalError =
      'Произошла ошибка. Пожалуйста, попробуйте позже.';
  static const String connectionError = 'Проверьте подключение к интернету';
  static const String authError = 'Ошибка авторизации';

  // ДИАЛОГИ
  // -------------------
  static const String dialogConfirmTitle = 'Подтверждение';
  static const String dialogErrorTitle = 'Ошибка';
  static const String dialogInfoTitle = 'Информация';
}
