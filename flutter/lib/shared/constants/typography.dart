import 'package:flutter/material.dart';
import 'package:luxury_app/theme/theme.dart'; // Used by AppTypography.getLogoTextStyle

// Original AppTypography class from typography.dart
class AppTypography {
  const AppTypography._();

  static const String fontFamilyPrimary = 'Geologica';
  static const String fontFamilyLogo = 'Geologica';
  static const String fontFamilyCode = 'PT Mono';
  static const String fontFamilySecondary = 'Geologica';

  // Оптимизированные размеры для Geologica
  static const double displayLarge = 56.0;
  static const double displayMedium = 44.0;
  static const double displaySmall = 36.0;
  static const double headlineLarge = 32.0;
  static const double headlineMedium = 28.0;
  static const double headlineSmall = 24.0;
  static const double titleLarge = 22.0;
  static const double titleMedium = 16.0;
  static const double titleSmall = 14.0;
  static const double bodyLarge = 16.0;
  static const double bodyMedium = 14.0;
  static const double bodySmall = 12.0;
  static const double labelLarge = 14.0;
  static const double labelMedium = 12.0;
  static const double labelSmall = 11.0;
  static const double codeFontSize = 14.0;

  // Оптимизированные межстрочные интервалы для Geologica
  static const double lineHeightDisplay = 1.2;
  static const double lineHeightHeadline = 1.3;
  static const double lineHeightTitle = 1.4;
  static const double lineHeightBody = 1.45;
  static const double lineHeightCode = 1.4;
  static const double lineHeightCompact = 1.15;

  // Веса шрифтов: более жирные заголовки, более тонкий основной текст
  static const FontWeight weightLight = FontWeight.w300;
  static const FontWeight weightRegular = FontWeight.w400;
  static const FontWeight weightMedium = FontWeight.w500;
  static const FontWeight weightSemiBold = FontWeight.w600;
  static const FontWeight weightBold = FontWeight.w700;

  // Оптимизированные межбуквенные расстояния для Geologica
  static const double letterSpacingDisplay = -0.3;
  static const double letterSpacingHeadline = -0.2;
  static const double letterSpacingTitle = -0.05;
  static const double letterSpacingBody = 0.05;
  static const double letterSpacingLabel = 0.2;
  static const double letterSpacingLogo = 0.5;
  static const double letterSpacingCode = 0.0;

  static TextStyle getCodeTextStyle({double? fontSize}) {
    return TextStyle(
      fontFamily: fontFamilyCode,
      fontSize: fontSize ?? codeFontSize,
      letterSpacing: letterSpacingCode,
      height: lineHeightCode,
    );
  }

  static TextStyle getLogoTextStyle({
    required bool isLight,
    required bool isLuxury,
  }) {
    return TextStyle(
      fontFamily: fontFamilyLogo,
      fontSize: titleLarge,
      fontWeight: weightBold,
      letterSpacing: letterSpacingLogo,
      color: isLuxury ? AppColors.viewerText : AppColors.brandGold,
    );
  }
}

/// Централизованный конфиг для всей типографики приложения и markdown
class AppTypographyConfig {
  final double scaleFactor;
  const AppTypographyConfig({this.scaleFactor = 1.0});

  // Заголовки
  TextStyle get h1 => _scaled(
    AppTypography.titleLarge,
    AppTypography.weightBold,
    AppTypography.letterSpacingTitle,
    AppTypography.lineHeightTitle,
  );
  TextStyle get h2 => _scaled(
    AppTypography.titleMedium,
    AppTypography.weightBold,
    AppTypography.letterSpacingTitle,
    AppTypography.lineHeightTitle,
  );
  TextStyle get h3 => _scaled(
    AppTypography.titleSmall,
    AppTypography.weightSemiBold,
    AppTypography.letterSpacingTitle,
    AppTypography.lineHeightTitle,
  );
  // Параграфы и списки
  TextStyle get body => _scaled(
    AppTypography.bodyLarge,
    AppTypography.weightRegular,
    AppTypography.letterSpacingBody,
    AppTypography.lineHeightBody,
  );
  TextStyle get bodySmall => _scaled(
    AppTypography.bodySmall,
    AppTypography.weightLight,
    AppTypography.letterSpacingBody,
    AppTypography.lineHeightBody,
  );
  // Код
  TextStyle get code => _scaled(
    AppTypography.codeFontSize,
    AppTypography.weightRegular,
    AppTypography.letterSpacingCode,
    AppTypography.lineHeightCode,
    fontFamily: AppTypography.fontFamilyCode,
  );
  // Цитата
  TextStyle get blockquote => _scaled(
    AppTypography.bodyLarge,
    AppTypography.weightMedium,
    AppTypography.letterSpacingBody,
    AppTypography.lineHeightBody,
    fontStyle: FontStyle.italic,
  );
  // Кнопки и лейблы
  TextStyle get button => _scaled(
    AppTypography.labelLarge,
    AppTypography.weightMedium,
    AppTypography.letterSpacingLabel,
    AppTypography.lineHeightCompact,
  );
  TextStyle get label => _scaled(
    AppTypography.labelMedium,
    AppTypography.weightMedium,
    AppTypography.letterSpacingLabel,
    AppTypography.lineHeightCompact,
  );

  TextStyle _scaled(
    double size,
    FontWeight weight,
    double letterSpacing,
    double height, {
    String? fontFamily,
    FontStyle? fontStyle,
  }) {
    return TextStyle(
      fontFamily: fontFamily ?? AppTypography.fontFamilyPrimary,
      fontSize: size * scaleFactor,
      fontWeight: weight,
      letterSpacing: letterSpacing,
      height: height,
      fontStyle: fontStyle,
    );
  }

  /// Фабрика для быстрого доступа к конфигу с нужным масштабом
  static AppTypographyConfig of(BuildContext context, {double? scale}) {
    // Можно интегрировать с настройками пользователя
    final scaler = MediaQuery.of(context).textScaler;
    return AppTypographyConfig(scaleFactor: scale ?? scaler.scale(1.0));
  }
}
