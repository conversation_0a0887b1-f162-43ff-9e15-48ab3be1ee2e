import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Константы для AI сервисов
class AIConstants {
  // API ключи из переменных окружения (без fallback)
  static String get openRouterApiKey => dotenv.env['OPENROUTER_API_KEY']!;
  static String get openAIApiKey => dotenv.env['OPENAI_API_KEY']!;

  // Базовые URL из переменных окружения (без fallback)
  static String get openRouterBaseUrl => dotenv.env['OPENROUTER_BASE_URL']!;
  static String get openAIBaseUrl => dotenv.env['OPENAI_BASE_URL']!;

  // Модели из переменных окружения (без fallback)
  static String get llmModel => dotenv.env['OPENROUTER_LLM_MODEL']!;
  static String get transcribeModel => dotenv.env['OPENAI_TRANSCRIBE_MODEL']!;

  // Системный промпт
  static const String systemPrompt =
      'Ты - AI-ассистент компании Luxury Sound. Отвечай профессионально и дружелюбно на русском языке.';

  // Проверка наличия API ключей
  static bool get hasRequiredKeys =>
      dotenv.env.containsKey('OPENROUTER_API_KEY') &&
      dotenv.env.containsKey('OPENAI_API_KEY') &&
      dotenv.env.containsKey('OPENROUTER_BASE_URL') &&
      dotenv.env.containsKey('OPENAI_BASE_URL') &&
      dotenv.env.containsKey('OPENROUTER_LLM_MODEL') &&
      dotenv.env.containsKey('OPENAI_TRANSCRIBE_MODEL');
}
