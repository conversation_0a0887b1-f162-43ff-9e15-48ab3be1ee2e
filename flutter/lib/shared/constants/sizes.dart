import 'package:flutter/material.dart';

/// Содержит все константы размеров и отступов для приложения
/// 
/// Используется для обеспечения единообразия размеров в приложении.
/// Все размеры указаны в логических пикселях (dp).
class AppSizes {
  // Приватный конструктор, чтобы запретить создание экземпляров
  const AppSizes._();

  // =====================================================================
  // БАЗОВЫЕ ОТСТУПЫ
  // =====================================================================
  /// Маленький отступ (4dp)
  static const double paddingXS = 4.0;
  /// Небольшой отступ (8dp)
  static const double paddingS = 8.0;
  /// Средний отступ (16dp) - основной для большинства компонентов
  static const double paddingM = 16.0;
  /// Большой отступ (24dp)
  static const double paddingL = 24.0;
  /// Очень большой отступ (32dp)
  static const double paddingXL = 32.0;

  // =====================================================================
  // РАДИУСЫ СКРУГЛЕНИЯ
  // =====================================================================
  /// Средний радиус скругления (8dp) - наиболее часто используемый
  static const double radiusM = 8.0;
  /// Большой радиус скругления (16dp)
  static const double radiusL = 16.0;

  // =====================================================================
  // РАЗМЕРЫ КОМПОНЕНТОВ ИНТЕРФЕЙСА
  // =====================================================================
  // Иконки
  /// Стандартный размер иконок (24dp)
  static const double iconSize = 24.0;

  // Элементы навигации
  /// Размер иконок в навигационных элементах (20dp)
  static const double navIconSize = 20.0;

  
  // =====================================================================
  // BREAKPOINTS ДЛЯ АДАПТИВНОСТИ
  // =====================================================================
  /// Breakpoint для мобильных устройств (до 680px)
  static const double mobileBreakpoint = 680.0;
  /// Breakpoint для планшетов (680-1200px)
  static const double tabletBreakpoint = 1200.0;
  /// Breakpoint для desktop (1200px+)
  static const double desktopBreakpoint = 1200.0;
  
  // =====================================================================
  // КОЭФФИЦИЕНТЫ МАСШТАБИРОВАНИЯ И ПЛОТНОСТИ
  // =====================================================================
  /// Малый коэффициент масштабирования (0.85)
  static const double scaleSmall = 0.85;
  /// Компактный коэффициент плотности (0.85)
  static const double densityCompact = 0.85;
  

  
  /// Получает максимальную ширину сообщения в чате в зависимости от размера экрана
  /// 
  /// @param context Контекст Flutter для получения размеров экрана
  /// @return Максимальная ширина сообщения (75% от ширины экрана)
  static double getMessageMaxWidth(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width * 0.75;
  }
}
