/// Централизованная конфигурация всех API-сервисов
class ApiConstants {
  /// Базовый URL для Core API (staging)
  static const String coreApiUrl = 'https://beta-api-ls.zvonko-app.ru';

  /// URL для аудио файлов (всегда удаленный сервер)
  static const String audioFilesUrl = 'https://api.lsound.info';

  /// Базовый URL для изображений и статических файлов
  static String get baseImageUrl => coreApiUrl;

  // Endpoints для Core API
  static const String authTokenEndpoint = '/auth/token';
  static const String authRegisterEndpoint = '/auth/register';
  static const String userProfileEndpoint = '/auth/users/me';
  static const String authLoginEndpoint = '/auth/login';
  static const String chatsEndpoint = '/chats';
  static const String uploadEndpoint = '/upload';
  static const String transcribeEndpoint = '/transcribe';
  static const String wikiEndpoint = '/wiki';
  static const String newsEndpoint = '/news';
}
