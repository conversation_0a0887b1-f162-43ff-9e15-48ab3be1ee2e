import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:luxury_app/shared/widgets/mermaid_service.dart';
import 'package:share_plus/share_plus.dart';

import 'zoom_controls.dart';

class MermaidPreview extends StatefulWidget {
  final String code;
  const MermaidPreview({super.key, required this.code});

  @override
  State<MermaidPreview> createState() => _MermaidPreviewState();
}

class _MermaidPreviewState extends State<MermaidPreview> {
  double _scale = 1.0;
  final TransformationController _transformationController =
      TransformationController();

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  void _handleZoomIn() {
    setState(() {
      _scale = (_scale * 1.25).clamp(0.3, 5.0);
      _transformationController.value = Matrix4.identity()..scale(_scale);
    });
  }

  void _handleZoomOut() {
    setState(() {
      _scale = (_scale / 1.25).clamp(0.3, 5.0);
      _transformationController.value = Matrix4.identity()..scale(_scale);
    });
  }

  void _handleReset() {
    setState(() {
      _scale = 1.0;
      _transformationController.value = Matrix4.identity();
    });
  }

  void _handleDoubleTap() {
    setState(() {
      if ((_scale - 1.0).abs() < 0.01) {
        _scale = 2.0;
      } else {
        _scale = 1.0;
      }
      _transformationController.value = Matrix4.identity()..scale(_scale);
    });
  }

  Future<void> _shareSvg(BuildContext context) async {
    try {
      final svg = await MermaidService.render(widget.code);
      final bytes = Uint8List.fromList(svg.codeUnits);
      final tempDir = Directory.systemTemp;
      final file = await File(
        "${tempDir.path}/diagram.svg",
      ).writeAsBytes(bytes);
      if (!context.mounted) return;
      await SharePlus.instance.share(
        ShareParams(files: [XFile(file.path)], text: 'Диаграмма Mermaid (SVG)'),
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Ошибка при экспорте SVG: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final htmlContent = '''<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
  <style>
    body { margin:0; padding:0; background: transparent; }
    .mermaid { width:100%; }
  </style>
</head>
<body>
  <div class="mermaid">${widget.code}</div>
  <script>
    mermaid.initialize({ startOnLoad:true, securityLevel:'loose', theme:'dark' });
  </script>
</body>
</html>''';

    return Stack(
      children: [
        Container(
          height: 320,
          margin: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).dividerColor.withAlpha(77),
            ),
            color: Theme.of(context).scaffoldBackgroundColor,
          ),
          clipBehavior: Clip.antiAlias,
          child: GestureDetector(
            onDoubleTap: _handleDoubleTap,
            child: Listener(
              onPointerSignal: (event) {
                if (event is PointerScrollEvent &&
                    (event.kind == PointerDeviceKind.mouse ||
                        event.kind == PointerDeviceKind.trackpad)) {
                  if (event.scrollDelta.dy < 0) {
                    _handleZoomIn();
                  } else if (event.scrollDelta.dy > 0) {
                    _handleZoomOut();
                  }
                }
              },
              child: InteractiveViewer(
                minScale: 0.3,
                maxScale: 5.0,
                transformationController: _transformationController,
                panEnabled: true,
                scaleEnabled: true,
                child: InAppWebView(
                  initialData: InAppWebViewInitialData(
                    data: htmlContent,
                    mimeType: 'text/html',
                    encoding: 'utf-8',
                  ),
                  initialSettings: InAppWebViewSettings(
                    transparentBackground: true,
                    javaScriptEnabled: true,
                    disableContextMenu: true,
                    supportZoom: false,
                  ),
                ),
              ),
            ),
          ),
        ),
        // Кнопки управления масштабом
        Positioned(
          bottom: 10,
          left: 10,
          child: ZoomControls(
            scale: _scale,
            onZoomIn: _handleZoomIn,
            onZoomOut: _handleZoomOut,
            onReset: _handleReset,
          ),
        ),
        // Кнопка экспорта SVG
        Positioned(
          bottom: 10,
          right: 10,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () => _shareSvg(context),
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface.withAlpha(217),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(color: Colors.black.withAlpha(18), blurRadius: 4),
                  ],
                ),
                padding: const EdgeInsets.all(6),
                child: const Icon(Icons.share_rounded, size: 20),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
