import 'package:flutter/material.dart';

class ZoomControls extends StatelessWidget {
  final double scale;
  final VoidCallback onZoomIn;
  final VoidCallback onZoomOut;
  final VoidCallback onReset;

  const ZoomControls({
    super.key,
    required this.scale,
    required this.onZoomIn,
    required this.onZoomOut,
    required this.onReset,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(icon: const Icon(Icons.zoom_out), onPressed: onZoomOut),
        Text(
          scale.toStringAsFixed(2),
          style: Theme.of(context).textTheme.bodySmall,
        ),
        IconButton(icon: const Icon(Icons.zoom_in), onPressed: onZoomIn),
        IconButton(
          icon: const Icon(Icons.center_focus_strong),
          tooltip: 'Сбросить масштаб',
          onPressed: onReset,
        ),
      ],
    );
  }
}
