import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:luxury_app/shared/widgets/video_utils.dart';
import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

/// Универсальный видеоплеер для Markdown
class MarkdownVideoWidget extends StatefulWidget {
  final String url;
  final Map<String, String> attributes;
  final double? width;
  final double? height;

  const MarkdownVideoWidget({
    super.key,
    required this.url,
    this.attributes = const {},
    this.width,
    this.height,
  });

  @override
  State<MarkdownVideoWidget> createState() => _MarkdownVideoWidgetState();
}

class _MarkdownVideoWidgetState extends State<MarkdownVideoWidget> {
  VideoType? _videoType;
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;
  YoutubePlayerController? _youtubeController;
  bool _isLoading = true;
  String? _error;
  bool _isPlayerReady = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _disposeControllers() {
    _chewieController?.dispose();
    _videoController?.dispose();
    _youtubeController?.dispose();
  }

  void _listener() {
    if (_isPlayerReady && mounted && !_youtubeController!.value.isFullScreen) {
      setState(() {});
    }
  }

  Future<void> _initializeVideo() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      _videoType = VideoUtils.getVideoType(widget.url);

      switch (_videoType) {
        case VideoType.youtube:
          await _initializeYouTubePlayer();
          break;
        case VideoType.file:
          await _initializeVideoPlayer();
          break;
        case VideoType.unknown:
          setState(() {
            _error = 'Неподдерживаемый формат видео';
            _isLoading = false;
          });
          break;
        case null:
          setState(() {
            _error = 'Ошибка определения типа видео';
            _isLoading = false;
          });
          break;
      }
    } catch (e) {
      setState(() {
        _error = 'Ошибка загрузки видео: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _initializeYouTubePlayer() async {
    final videoId = VideoUtils.extractYouTubeVideoId(widget.url);
    if (videoId == null) {
      setState(() {
        _error = 'Не удалось извлечь ID YouTube видео';
        _isLoading = false;
      });
      return;
    }

    _youtubeController = YoutubePlayerController(
      initialVideoId: videoId,
      flags: const YoutubePlayerFlags(
        mute: false,
        autoPlay: false,
        disableDragSeek: false,
        loop: false,
        isLive: false,
        forceHD: false,
        enableCaption: true,
      ),
    )..addListener(_listener);

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _initializeVideoPlayer() async {
    try {
      final uri = Uri.parse(widget.url);
      _videoController = VideoPlayerController.networkUrl(uri);
      await _videoController!.initialize();

      _chewieController = ChewieController(
        videoPlayerController: _videoController!,
        autoPlay: false,
        looping: false,
        showControls: true,
        allowMuting: true,
        allowFullScreen: true,
        allowPlaybackSpeedChanging: true,
        aspectRatio: _videoController!.value.aspectRatio,
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Ошибка инициализации видеоплеера: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    switch (_videoType) {
      case VideoType.youtube:
        return _buildYouTubePlayer();
      case VideoType.file:
        return _buildVideoPlayer();
      default:
        return _buildErrorWidget();
    }
  }

  Widget _buildLoadingWidget() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Загрузка видео...'),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              _error ?? 'Ошибка загрузки видео',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              widget.url,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildYouTubePlayer() {
    if (_youtubeController == null) {
      return _buildErrorWidget();
    }

    return YoutubePlayerBuilder(
      onExitFullScreen: () {
        // Обработка выхода из полноэкранного режима
      },
      player: YoutubePlayer(
        controller: _youtubeController!,
        showVideoProgressIndicator: true,
        onReady: () {
          _isPlayerReady = true;
        },
      ),
      builder: (context, player) {
        return player;
      },
    );
  }

  Widget _buildVideoPlayer() {
    if (_chewieController == null || _videoController == null) {
      return _buildErrorWidget();
    }

    final aspectRatio = _videoController!.value.aspectRatio;
    final isVertical = aspectRatio < 1.0;

    if (isVertical) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final width = constraints.maxWidth * 0.9;
            final height = width / aspectRatio;
            final maxHeight = MediaQuery.of(context).size.height * 0.7;
            final constrainedHeight = height > maxHeight ? maxHeight : height;
            final actualWidth = constrainedHeight * aspectRatio;

            return Center(
              child: SizedBox(
                width: actualWidth,
                height: constrainedHeight,
                child: ClipRect(
                  child: AspectRatio(
                    aspectRatio: aspectRatio,
                    child: Chewie(controller: _chewieController!),
                  ),
                ),
              ),
            );
          },
        ),
      );
    } else {
      return ClipRect(
        child: AspectRatio(
          aspectRatio: aspectRatio,
          child: Chewie(controller: _chewieController!),
        ),
      );
    }
  }
}
