import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/services/background_sync_service.dart';

/// Провайдер для статуса синхронизации
final syncStatusProvider = StreamProvider<SyncStatus>((ref) {
  final backgroundSyncService = ref.read(backgroundSyncServiceProvider);
  return backgroundSyncService.syncStatusStream;
});

/// Индикатор статуса синхронизации
class SyncStatusIndicator extends ConsumerWidget {
  const SyncStatusIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncStatusAsync = ref.watch(syncStatusProvider);

    return syncStatusAsync.when(
      data: (status) {
        // Показываем индикатор только при синхронизации или ошибке
        if (status == SyncStatus.idle || status == SyncStatus.completed) {
          return const SizedBox.shrink();
        }

        Color statusColor;
        IconData statusIcon;
        String statusText;

        switch (status) {
          case SyncStatus.syncing:
            statusColor = Colors.blue;
            statusIcon = Icons.sync;
            statusText = 'Синхронизация...';
            break;
          case SyncStatus.error:
            statusColor = Colors.red;
            statusIcon = Icons.sync_problem;
            statusText = 'Ошибка синхронизации';
            break;
          case SyncStatus.offline:
            statusColor = Colors.orange;
            statusIcon = Icons.cloud_off;
            statusText = 'Offline';
            break;
          default:
            return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: statusColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              status == SyncStatus.syncing
                  ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                    ),
                  )
                  : Icon(statusIcon, size: 16, color: statusColor),
              const SizedBox(width: 6),
              Text(
                statusText,
                style: TextStyle(
                  fontSize: 12,
                  color: statusColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, _) => const SizedBox.shrink(),
    );
  }
}

/// Кнопка принудительной синхронизации
class ForceSyncButton extends ConsumerWidget {
  const ForceSyncButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncStatusAsync = ref.watch(syncStatusProvider);

    return syncStatusAsync.when(
      data: (status) {
        final isDisabled =
            status == SyncStatus.syncing || status == SyncStatus.offline;

        return IconButton(
          onPressed:
              isDisabled
                  ? null
                  : () async {
                    final backgroundSyncService = ref.read(
                      backgroundSyncServiceProvider,
                    );
                    await backgroundSyncService.forceSync();
                  },
          icon: Icon(
            status == SyncStatus.syncing ? Icons.sync : Icons.refresh,
            color: isDisabled ? Colors.grey : null,
          ),
          tooltip:
              isDisabled
                  ? (status == SyncStatus.offline
                      ? 'Нет подключения'
                      : 'Синхронизация...')
                  : 'Принудительная синхронизация',
        );
      },
      loading:
          () => const IconButton(
            onPressed: null,
            icon: Icon(Icons.refresh, color: Colors.grey),
          ),
      error:
          (_, _) => const IconButton(
            onPressed: null,
            icon: Icon(Icons.refresh, color: Colors.grey),
          ),
    );
  }
}
