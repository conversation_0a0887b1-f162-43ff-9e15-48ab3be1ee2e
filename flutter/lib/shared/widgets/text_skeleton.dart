import 'package:flutter/material.dart';

/// Простой skeleton-загрузчик для текста (shimmer-эффект не используется для минимализма и скорости)
class TextSkeleton extends StatelessWidget {
  final double width;
  final double height;
  final EdgeInsetsGeometry? margin;

  const TextSkeleton({
    super.key,
    this.width = double.infinity,
    this.height = 18,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin ?? const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}
