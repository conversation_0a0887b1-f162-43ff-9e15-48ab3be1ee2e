import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/shared/constants/sizes.dart';

/// Унифицированный элемент списка для drawer
/// Использует тот же стиль что и иерархии Wiki
class DrawerListItem extends StatefulWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Function(String)? onEdit;
  final VoidCallback? onDelete;
  final bool isActive;
  final bool isEditable;
  final int level;
  final Color? activeColor;
  final Color? titleColor;
  final FontWeight? titleFontWeight;

  const DrawerListItem({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.onEdit,
    this.onDelete,
    this.isActive = false,
    this.isEditable = false,
    this.level = 0,
    this.activeColor,
    this.titleColor,
    this.titleFontWeight,
  });

  @override
  State<DrawerListItem> createState() => _DrawerListItemState();
}

class _DrawerListItemState extends State<DrawerListItem> {
  bool _isEditing = false;
  late TextEditingController _editController;

  @override
  void initState() {
    super.initState();
    _editController = TextEditingController(text: widget.title);
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color:
            widget.isActive
                ? (widget.activeColor ??
                    colorScheme.primaryContainer.withAlpha(76))
                : null,
        borderRadius: BorderRadius.circular(6),
      ),
      child: InkWell(
        onTap: _isEditing ? null : widget.onTap,
        onLongPress: widget.isEditable ? _startEditing : widget.onLongPress,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: EdgeInsets.only(
            left: AppSizes.paddingS + (widget.level * AppSizes.paddingM),
            right: AppSizes.paddingS,
            top: 4.0,
            bottom: 4.0,
          ),
          child: Row(
            children: [
              // Иконка
              if (widget.leading != null) ...[
                widget.leading!,
                SizedBox(width: AppSizes.paddingM),
              ],
              // Контент
              Expanded(child: _isEditing ? _buildEditField() : _buildContent()),
              // Trailing элементы
              if (_isEditing)
                _buildEditActions()
              else if (_buildTrailing() != null)
                _buildTrailing()!,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          widget.title,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight:
                widget.titleFontWeight ??
                (widget.isActive ? FontWeight.w500 : FontWeight.normal),
            color:
                widget.titleColor ??
                (widget.isActive
                    ? theme.colorScheme.onPrimaryContainer
                    : theme.colorScheme.onSurface),
          ),
          overflow: TextOverflow.ellipsis,
        ),
        if (widget.subtitle != null) ...[
          const SizedBox(height: 2),
          Text(
            widget.subtitle!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(153),
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildEditField() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return CallbackShortcuts(
      bindings: {const SingleActivator(LogicalKeyboardKey.escape): _cancelEdit},
      child: TextField(
        controller: _editController,
        autofocus: true,
        style: theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(color: colorScheme.primary, width: 1.5),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(color: colorScheme.primary, width: 1.5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(color: colorScheme.primary, width: 1.5),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 6,
            vertical: 4,
          ),
          isDense: true,
          filled: true,
          fillColor: colorScheme.surface,
        ),
        onSubmitted: _saveEdit,
        onTapOutside: (_) => _cancelEdit(),
      ),
    );
  }

  Widget _buildEditActions() {
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () => _saveEdit(_editController.text),
            child: Container(
              padding: const EdgeInsets.all(6),
              child: Icon(
                LucideIcons.check,
                size: 16,
                color: colorScheme.primary,
              ),
            ),
          ),
        ),
        const SizedBox(width: 2),
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: _cancelEdit,
            child: Container(
              padding: const EdgeInsets.all(6),
              child: Icon(
                LucideIcons.x,
                size: 16,
                color: colorScheme.onSurface.withAlpha(153),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget? _buildTrailing() {
    if (widget.trailing != null) {
      return widget.trailing;
    }

    if (widget.onDelete != null) {
      return PopupMenuButton<String>(
        icon: Icon(
          LucideIcons.moreVertical,
          size: AppSizes.navIconSize * AppSizes.scaleSmall,
          color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
        ),
        itemBuilder:
            (context) => [
              if (widget.onEdit != null)
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(LucideIcons.edit2, size: 16),
                      SizedBox(width: 8),
                      Text('Редактировать'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(LucideIcons.trash2, size: 16),
                    SizedBox(width: 8),
                    Text('Удалить'),
                  ],
                ),
              ),
            ],
        onSelected: (value) {
          switch (value) {
            case 'edit':
              _startEditing();
              break;
            case 'delete':
              widget.onDelete?.call();
              break;
          }
        },
      );
    }

    return null;
  }

  void _startEditing() {
    if (!widget.isEditable) return;
    setState(() {
      _isEditing = true;
      _editController.text = widget.title;
    });
  }

  void _saveEdit(String newTitle) {
    if (newTitle.trim().isEmpty) {
      _cancelEdit();
      return;
    }

    setState(() {
      _isEditing = false;
    });

    if (newTitle.trim() != widget.title) {
      widget.onEdit?.call(newTitle.trim());
    }
  }

  void _cancelEdit() {
    setState(() {
      _isEditing = false;
      _editController.text = widget.title;
    });
  }
}
