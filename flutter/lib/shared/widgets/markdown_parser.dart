import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:luxury_app/shared/constants/typography.dart';
import 'package:luxury_app/shared/widgets/markdown_image_widget.dart';
import 'package:luxury_app/shared/widgets/markdown_video_widget.dart';
import 'package:luxury_app/shared/widgets/mermaid_preview.dart';
import 'package:luxury_app/shared/widgets/video_utils.dart';
import 'package:markdown_widget/markdown_widget.dart';
import 'package:url_launcher/url_launcher.dart';

/// Вспомогательный класс для логирования в markdown_parser
class _MarkdownLogger with LoggerMixin {
  static final _instance = _MarkdownLogger._();
  _MarkdownLogger._();

  static void logUrlError(String message, [Object? error]) {
    _instance.logError(message, error);
  }
}

class ParsedMarkdown {
  final List<Widget> widgets;
  final int imageCount;
  ParsedMarkdown(this.widgets, this.imageCount);
}

/// Данные для изолированного парсинга
class MarkdownParseData {
  final String content;
  final bool countImages;

  const MarkdownParseData({required this.content, this.countImages = false});
}

/// Результат изолированного парсинга
class MarkdownParseResult {
  final List<String> textSegments;
  final List<String> mermaidCodes;
  final int imageCount;

  const MarkdownParseResult({
    required this.textSegments,
    required this.mermaidCodes,
    required this.imageCount,
  });
}

class ImageWithCallback extends StatelessWidget {
  final String url;
  final Map<String, String> attributes;
  final VoidCallback? onLoaded;

  const ImageWithCallback({
    super.key,
    required this.url,
    required this.attributes,
    this.onLoaded,
  });

  ImageWithCallback buildWithCallback(VoidCallback onLoaded) =>
      ImageWithCallback(url: url, attributes: attributes, onLoaded: onLoaded);

  @override
  Widget build(BuildContext context) {
    return CachedImageWidget(key: ValueKey(url), url: url, onLoaded: onLoaded);
  }
}

class VideoWithCallback extends StatelessWidget {
  final String url;
  final Map<String, String> attributes;
  final VoidCallback? onLoaded;

  const VideoWithCallback({
    super.key,
    required this.url,
    required this.attributes,
    this.onLoaded,
  });

  VideoWithCallback buildWithCallback(VoidCallback onLoaded) =>
      VideoWithCallback(url: url, attributes: attributes, onLoaded: onLoaded);

  @override
  Widget build(BuildContext context) {
    // Для видео мы сразу вызываем onLoaded, так как видеоплеер управляет своей загрузкой
    WidgetsBinding.instance.addPostFrameCallback((_) {
      onLoaded?.call();
    });
    return MarkdownVideoWidget(
      key: ValueKey(url),
      url: url,
      attributes: attributes,
    );
  }
}

/// Конфигурация для markdown виджетов
MarkdownConfig _createMarkdownConfig({bool withImageCallback = false}) {
  final typography = typographyStatic;
  return MarkdownConfig(
    configs: [
      TableConfig(
        wrapper:
            (child) => SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: child,
            ),
      ),
      _NoDividerH1Config(),
      _NoDividerH2Config(),
      _NoDividerH3Config(),
      H4Config(
        style: typography.body.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: typography.body.fontSize! * 0.95,
        ),
      ),
      H5Config(
        style: typography.body.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: typography.body.fontSize! * 0.9,
        ),
      ),
      H6Config(
        style: typography.body.copyWith(
          fontWeight: FontWeight.w400,
          fontSize: typography.body.fontSize! * 0.85,
        ),
      ),
      PConfig(textStyle: typography.body),
      CodeConfig(style: typography.code),
      HrConfig(height: 0.5),
      ImgConfig(
        builder: (url, attributes) {
          // Проверяем, является ли URL видео
          final videoType = VideoUtils.getVideoType(url);
          if (videoType != VideoType.unknown) {
            // Это видео - используем видеоплеер
            return withImageCallback
                ? VideoWithCallback(url: url, attributes: attributes)
                : MarkdownVideoWidget(
                  key: ValueKey(url),
                  url: url,
                  attributes: attributes,
                );
          } else {
            // Это изображение - используем обычную логику
            return withImageCallback
                ? ImageWithCallback(url: url, attributes: attributes)
                : CachedImageWidget(key: ValueKey(url), url: url);
          }
        },
      ),
      LinkConfig(
        onTap: (url) async {
          // Проверяем, является ли ссылка YouTube видео
          if (VideoUtils.isYouTubeUrl(url)) {
            // Для YouTube ссылок не открываем браузер, а показываем встроенный плеер
            return;
          }

          // Для остальных ссылок открываем в браузере
          try {
            final uri = Uri.parse(url);
            if (await canLaunchUrl(uri)) {
              await launchUrl(uri, mode: LaunchMode.externalApplication);
            } else {
              // Если не можем открыть ссылку, логируем ошибку
              _MarkdownLogger.logUrlError('Cannot launch URL: $url');
            }
          } catch (e) {
            // Обрабатываем ошибки парсинга URL
            _MarkdownLogger.logUrlError('Error launching URL: $url', e);
          }
        },
      ),
    ],
  );
}

/// Изолированная функция парсинга для compute()
MarkdownParseResult _parseMarkdownInIsolate(MarkdownParseData data) {
  // Предварительная обработка для YouTube-ссылок
  final processedContent = _preprocessMarkdownForVideo(data.content);

  final regex = RegExp(r'''```mermaid\n([\s\S]*?)```''', multiLine: true);
  final List<String> textSegments = [];
  final List<String> mermaidCodes = [];
  int imageCount = 0;
  int lastEnd = 0;

  for (final match in regex.allMatches(processedContent)) {
    final before = processedContent.substring(lastEnd, match.start);
    if (before.trim().isNotEmpty) {
      textSegments.add(before);
      if (data.countImages) {
        imageCount += _countImages(before);
      }
    }

    var code = match.group(1)!.trim();
    if (code.startsWith('```mermaid')) {
      code = code.substring('```mermaid'.length).trimLeft();
    }
    if (code.endsWith('```')) {
      code = code.substring(0, code.length - 3).trimRight();
    }
    mermaidCodes.add(code);
    lastEnd = match.end;
  }

  final after = processedContent.substring(lastEnd);
  if (after.trim().isNotEmpty) {
    textSegments.add(after);
    if (data.countImages) {
      imageCount += _countImages(after);
    }
  }

  return MarkdownParseResult(
    textSegments: textSegments,
    mermaidCodes: mermaidCodes,
    imageCount: imageCount,
  );
}

/// Элемент кэша с временной меткой
class _CacheEntry {
  final ParsedMarkdown result;
  final DateTime timestamp;

  _CacheEntry(this.result, this.timestamp);
}

/// Простой кэш для результатов парсинга с TTL
final Map<String, _CacheEntry> _parseCache = <String, _CacheEntry>{};
const int _maxCacheSize = 20; // Уменьшаем размер кэша для экономии памяти
const Duration _cacheTTL = Duration(minutes: 30); // Время жизни кэша - 30 минут

/// Очищает устаревшие элементы из кэша
void _cleanupExpiredCache() {
  final now = DateTime.now();
  final expiredKeys = <String>[];

  for (final entry in _parseCache.entries) {
    if (now.difference(entry.value.timestamp) >= _cacheTTL) {
      expiredKeys.add(entry.key);
    }
  }

  for (final key in expiredKeys) {
    _parseCache.remove(key);
  }
}

/// Предварительная обработка Markdown для замены YouTube-ссылок и HTML video тегов
String _preprocessMarkdownForVideo(String data) {
  String processedData = data;

  // 1. Заменяем YouTube ссылки в формате [текст](URL) на ![текст](URL)
  final youtubeRegex = RegExp(
    r'\[([^\]]*)\]\((https?://(www\.)?(youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/|youtube\.com/v/)[^\)]+)\)',
    caseSensitive: false,
  );

  processedData = processedData.replaceAllMapped(youtubeRegex, (match) {
    final text = match.group(1) ?? 'YouTube видео';
    final url = match.group(2) ?? '';
    return '![$text]($url)';
  });

  // 2. Заменяем HTML video теги на markdown изображения
  // Ищем source теги внутри video
  final videoTagRegex = RegExp(
    r'<video[^>]*>[\s\S]*?<source[^>]+src="([^"]+)"[^>]*>[\s\S]*?</video>',
    caseSensitive: false,
    multiLine: true,
  );

  processedData = processedData.replaceAllMapped(videoTagRegex, (match) {
    final videoUrl = match.group(1) ?? '';
    return '![Видео]($videoUrl)';
  });

  // 3. Простые video теги с src атрибутом
  final simpleVideoRegex = RegExp(
    r'<video[^>]+src="([^"]+)"[^>]*>[\s\S]*?</video>',
    caseSensitive: false,
    multiLine: true,
  );

  processedData = processedData.replaceAllMapped(simpleVideoRegex, (match) {
    final videoUrl = match.group(1) ?? '';
    return '![Видео]($videoUrl)';
  });

  return processedData;
}

/// Универсальная функция парсинга markdown с опциональным подсчетом изображений
Future<ParsedMarkdown> parseMarkdownWidgets(
  String data, {
  bool countImages = false,
}) async {
  // Предварительная обработка для YouTube-ссылок
  final processedData = _preprocessMarkdownForVideo(data);

  // Создаем ключ кэша
  final cacheKey = '${processedData.hashCode}_$countImages';

  // Проверяем кэш и его актуальность
  if (_parseCache.containsKey(cacheKey)) {
    final entry = _parseCache[cacheKey]!;
    final now = DateTime.now();

    // Проверяем, не истек ли TTL
    if (now.difference(entry.timestamp) < _cacheTTL) {
      return entry.result;
    } else {
      // Удаляем устаревший элемент
      _parseCache.remove(cacheKey);
    }
  }

  ParsedMarkdown result;

  // Для больших документов используем compute() - увеличиваем порог
  if (processedData.length > 10000) {
    final parseData = MarkdownParseData(
      content: processedData,
      countImages: countImages,
    );
    final isolateResult = await compute(_parseMarkdownInIsolate, parseData);
    result = _buildWidgetsFromResult(isolateResult, countImages);
  } else {
    // Для маленьких и средних документов парсим синхронно для лучшей производительности
    result = _parseMarkdownSync(processedData, countImages: countImages);
  }

  // Периодически очищаем устаревшие элементы кэша
  if (_parseCache.length % 5 == 0) {
    _cleanupExpiredCache();
  }

  // Сохраняем в кэш с ограничением размера
  if (_parseCache.length >= _maxCacheSize) {
    // Удаляем самый старый элемент
    final firstKey = _parseCache.keys.first;
    _parseCache.remove(firstKey);
  }
  _parseCache[cacheKey] = _CacheEntry(result, DateTime.now());

  return result;
}

/// Построение виджетов из результата изолированного парсинга
ParsedMarkdown _buildWidgetsFromResult(
  MarkdownParseResult result,
  bool countImages,
) {
  final mdConfig = _createMarkdownConfig(withImageCallback: countImages);
  final List<Widget> children = [];
  int mermaidIndex = 0;

  for (int i = 0; i < result.textSegments.length; i++) {
    final segment = result.textSegments[i];
    if (segment.trim().isNotEmpty) {
      final widget = MarkdownWidget(
        data: segment,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        config: mdConfig,
      );
      children.add(widget);
    }

    // Добавляем Mermaid диаграмму если есть
    if (mermaidIndex < result.mermaidCodes.length) {
      children.add(MermaidPreview(code: result.mermaidCodes[mermaidIndex]));
      mermaidIndex++;
    }
  }

  return ParsedMarkdown(children, result.imageCount);
}

/// Синхронная версия парсинга для маленьких документов
ParsedMarkdown _parseMarkdownSync(String data, {bool countImages = false}) {
  // Предварительная обработка для YouTube-ссылок
  final processedData = _preprocessMarkdownForVideo(data);

  final mdConfig = _createMarkdownConfig(withImageCallback: countImages);
  final regex = RegExp(r'''```mermaid\n([\s\S]*?)```''', multiLine: true);
  final List<Widget> children = [];
  int imageCount = 0;
  int lastEnd = 0;

  for (final match in regex.allMatches(processedData)) {
    final before = processedData.substring(lastEnd, match.start);
    if (before.trim().isNotEmpty) {
      final widget = MarkdownWidget(
        data: before,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        config: mdConfig,
      );
      children.add(widget);
      if (countImages) {
        imageCount += _countImages(before);
      }
    }

    var code = match.group(1)!.trim();
    if (code.startsWith('```mermaid')) {
      code = code.substring('```mermaid'.length).trimLeft();
    }
    if (code.endsWith('```')) {
      code = code.substring(0, code.length - 3).trimRight();
    }
    children.add(MermaidPreview(code: code));
    lastEnd = match.end;
  }

  final after = processedData.substring(lastEnd);
  if (after.trim().isNotEmpty) {
    final widget = MarkdownWidget(
      data: after,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      config: mdConfig,
    );
    children.add(widget);
    if (countImages) {
      imageCount += _countImages(after);
    }
  }

  return ParsedMarkdown(children, imageCount);
}

/// Подсчет изображений и видео в markdown тексте
int _countImages(String markdown) {
  final imgRegex = RegExp(r'!\[.*?\]\((.*?)\)');
  return imgRegex.allMatches(markdown).length;
}

/// Обратная совместимость - синхронная функция с подсчетом изображений
ParsedMarkdown parseMarkdownWidgetsWithImageCount(String data) {
  return _parseMarkdownSync(data, countImages: true);
}

// Статика для типографики (без контекста)
final typographyStatic = const AppTypographyConfig();

class _NoDividerH1Config extends H1Config {
  _NoDividerH1Config() : super(style: typographyStatic.h1);
  @override
  HeadingDivider? get divider => null;
}

class _NoDividerH2Config extends H2Config {
  _NoDividerH2Config() : super(style: typographyStatic.h2);
  @override
  HeadingDivider? get divider => null;
}

class _NoDividerH3Config extends H3Config {
  _NoDividerH3Config() : super(style: typographyStatic.h3);
  @override
  HeadingDivider? get divider => null;
}
