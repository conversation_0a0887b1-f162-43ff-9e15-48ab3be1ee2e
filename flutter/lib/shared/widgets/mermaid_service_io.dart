import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import 'base_mermaid_service.dart';

class NativeMermaidService implements BaseMermaidService {
  InAppWebViewController? _controller;
  HeadlessInAppWebView? _headless;
  bool _initialized = false;

  @override
  Future<void> dispose() async {
    _initialized = false;

    try {
      // Сначала очищаем контроллер
      _controller = null;

      // Затем корректно освобождаем headless webview
      if (_headless != null) {
        await _headless!.dispose();
        _headless = null;
      }
    } catch (e) {
      // Логируем ошибку, но не пробрасываем её дальше
      if (kDebugMode) {
        debugPrint('⚠️ Error disposing MermaidService: $e');
      }
    }
  }

  @override
  Future<String> render(String cleanCode) async {
    // Проверка контроллеров на null
    if (_headless == null || _controller == null || !_initialized) {
      // Если был предыдущий headless, корректно уничтожаем
      await dispose();
      final htmlDataUri =
          'data:text/html;charset=utf-8;base64,${base64Encode(utf8.encode(_htmlShell))}';
      final completer = Completer<void>();
      _headless = HeadlessInAppWebView(
        initialUrlRequest: URLRequest(url: WebUri(htmlDataUri)),
        onWebViewCreated: (controller) => _controller = controller,
        onLoadStop: (controller, url) => completer.complete(),
      );
      await _headless!.run();
      await completer.future;
      await _controller!.evaluateJavascript(
        source:
            "(() => { mermaid.initialize({ startOnLoad:false, securityLevel: 'loose', theme: 'dark' }); return 'OK'; })();",
      );
      await _controller!.evaluateJavascript(
        source: '''(() => {
          window.mermaidSvgChunk = function(start, end) {
            return window.mermaidSvg ? window.mermaidSvg.substring(start, end) : '';
          };
          window.mermaidSvgLength = function() {
            return window.mermaidSvg ? window.mermaidSvg.length : 0;
          };
          return 'OK';
        })();''',
      );
      _initialized = true;
    }

    final codeBase64 = base64Encode(utf8.encode(cleanCode));

    final controller = _controller;
    if (controller == null) {
      throw Exception('WebView controller не инициализирован');
    }

    final parseResult = await controller.evaluateJavascript(
      source:
          "(() => { try { const code = decodeURIComponent(escape(window.atob('$codeBase64'))); mermaid.parse(code); return 'OK'; } catch (e) { return JSON.stringify({error: e.message || e.toString()}); } })();",
    );

    if (parseResult is String && parseResult.startsWith('{')) {
      final err = jsonDecode(parseResult) as Map<String, dynamic>;
      throw Exception('Ошибка синтаксиса mermaid: ${err['error']}');
    }

    final jsResult =
        await controller.evaluateJavascript(
              source: '''
        (async () => {
          try {
            const code = decodeURIComponent(escape(window.atob('$codeBase64')));
            return await new Promise((resolve, reject) => {
              try {
                mermaid.mermaidAPI.render('id', code)
                  .then(result => {
                    window.mermaidSvg = result.svg;
                    resolve('OK');
                  })
                  .catch(err => {
                    reject(err);
                  });
              } catch (e) {
                reject(e);
              }
            });
          } catch (e) {
            return JSON.stringify({error: e.toString()});
          }
        })()
      ''',
            )
            as String;

    if (jsResult != 'OK') {
      try {
        final err = jsonDecode(jsResult);
        throw Exception('Mermaid SVG error: ${err['error']}');
      } catch (_) {
        throw Exception('Mermaid SVG error: $jsResult');
      }
    }

    final svgLength =
        await controller.evaluateJavascript(
              source: "(() => { return window.mermaidSvgLength(); })();",
            )
            as int;

    final chunkSize = 10000;
    final buffer = StringBuffer();
    for (int start = 0; start < svgLength; start += chunkSize) {
      final end =
          (start + chunkSize < svgLength) ? start + chunkSize : svgLength;
      final chunk =
          await controller.evaluateJavascript(
                source:
                    "(() => { return window.mermaidSvgChunk($start, $end); })();",
              )
              as String;
      buffer.write(chunk);
    }

    return buffer.toString();
  }

  final String _htmlShell = '''
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
      </head>
      <body></body>
    </html>
  ''';
}
