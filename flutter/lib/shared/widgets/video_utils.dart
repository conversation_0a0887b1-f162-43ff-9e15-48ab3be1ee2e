/// Утилиты для работы с видео
class VideoUtils {
  /// Определяет, является ли URL YouTube-ссылкой
  static bool isYouTubeUrl(String url) {
    final youtubeRegex = RegExp(
      r'^https?://(www\.)?(youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/|youtube\.com/v/)',
      caseSensitive: false,
    );
    return youtubeRegex.hasMatch(url);
  }

  /// Извлекает YouTube video ID из URL
  static String? extractYouTubeVideoId(String url) {
    final regexes = [
      RegExp(r'youtube\.com/watch\?v=([a-zA-Z0-9_-]+)', caseSensitive: false),
      RegExp(r'youtu\.be/([a-zA-Z0-9_-]+)', caseSensitive: false),
      RegExp(r'youtube\.com/embed/([a-zA-Z0-9_-]+)', caseSensitive: false),
      RegExp(r'youtube\.com/v/([a-zA-Z0-9_-]+)', caseSensitive: false),
    ];

    for (final regex in regexes) {
      final match = regex.firstMatch(url);
      if (match != null) {
        return match.group(1);
      }
    }
    return null;
  }

  /// Определяет, является ли URL видеофайлом по расширению
  static bool isVideoFile(String url) {
    final videoExtensions = [
      '.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', 
      '.wmv', '.m4v', '.3gp', '.ogv'
    ];
    
    final uri = Uri.tryParse(url);
    if (uri == null) return false;
    
    final path = uri.path.toLowerCase();
    return videoExtensions.any((ext) => path.endsWith(ext));
  }

  /// Определяет тип видео по URL
  static VideoType getVideoType(String url) {
    if (isYouTubeUrl(url)) {
      return VideoType.youtube;
    } else if (isVideoFile(url)) {
      return VideoType.file;
    } else {
      return VideoType.unknown;
    }
  }
}

/// Типы видео
enum VideoType {
  youtube,
  file,
  unknown,
}
