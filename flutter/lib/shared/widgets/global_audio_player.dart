import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/core/services/simple_audio_service.dart';
import 'package:luxury_app/features/audio/audio_player_widget.dart';
import 'package:luxury_app/navigation/route_config.dart';

/// Глобальный минимизируемый аудиоплеер
class GlobalAudioPlayer extends StatefulWidget {
  const GlobalAudioPlayer({super.key});

  @override
  State<GlobalAudioPlayer> createState() => _GlobalAudioPlayerState();
}

class _GlobalAudioPlayerState extends State<GlobalAudioPlayer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<SimpleAudioState>(
      stream: SimpleAudioService.instance.stateStream,
      initialData: SimpleAudioService.instance.currentState,
      builder: (context, snapshot) {
        final state = snapshot.data!;

        if (!state.showPlayer || state.currentFileId == null) {
          return const SizedBox.shrink();
        }

        // Обновляем анимацию в зависимости от состояния минимизации
        if (state.isMinimized) {
          _animationController.forward();
        } else {
          _animationController.reverse();
        }

        return AnimatedBuilder(
          animation: _slideAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(
                0,
                MediaQuery.of(context).size.height *
                    0.8 *
                    _slideAnimation.value,
              ),
              child:
                  state.isMinimized
                      ? _buildMinimizedPlayer(context, state)
                      : _buildFullPlayer(context, state),
            );
          },
        );
      },
    );
  }

  /// Строит минимизированный плеер
  Widget _buildMinimizedPlayer(BuildContext context, SimpleAudioState state) {
    final colorScheme = Theme.of(context).colorScheme;

    return Positioned(
      left: 16,
      right: 16,
      bottom: 16,
      child: GestureDetector(
        onTap: () {
          SimpleAudioService.instance.maximizePlayer();
        },
        child: Container(
          height: 64,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(32),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withAlpha(51),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Кнопка play/pause
              IconButton(
                icon: Icon(
                  state.isPlaying ? Icons.pause : Icons.play_arrow,
                  color: colorScheme.onSurface,
                ),
                onPressed: () {
                  if (state.isPlaying) {
                    SimpleAudioService.instance.pause();
                  } else {
                    SimpleAudioService.instance.playAudio(
                      fileId: state.currentFileId!,
                      audioUrl: state.audioUrl,
                      sourcePageId: state.sourcePageId,
                      sourcePageTitle: state.sourcePageTitle,
                    );
                  }
                },
              ),

              // Информация о треке
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      state.sourcePageTitle ?? 'Аудио Wiki',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '${_formatDuration(state.position)} / ${_formatDuration(state.duration)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withAlpha(153),
                      ),
                    ),
                  ],
                ),
              ),

              // Кнопка перехода к источнику
              IconButton(
                icon: Icon(
                  LucideIcons.externalLink,
                  color: colorScheme.onSurface.withAlpha(179),
                  size: 18,
                ),
                onPressed: () {
                  if (state.sourcePageId != null) {
                    context.go(
                      RouteConfig.wikiPath.replaceFirst(
                        ':pageId',
                        state.sourcePageId!,
                      ),
                    );
                  }
                },
                tooltip: 'Перейти к источнику',
              ),

              // Кнопка закрытия
              IconButton(
                icon: Icon(
                  LucideIcons.x,
                  color: colorScheme.onSurface.withAlpha(179),
                  size: 18,
                ),
                onPressed: () {
                  SimpleAudioService.instance.stop();
                },
                tooltip: 'Закрыть',
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Строит полный плеер
  Widget _buildFullPlayer(BuildContext context, SimpleAudioState state) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Заголовок с кнопками
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                // Кнопка перехода к источнику
                IconButton(
                  icon: Icon(
                    LucideIcons.externalLink,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha(179),
                    size: 18,
                  ),
                  onPressed: () {
                    if (state.sourcePageId != null) {
                      context.go(
                        RouteConfig.wikiPath.replaceFirst(
                          ':pageId',
                          state.sourcePageId!,
                        ),
                      );
                    }
                  },
                  tooltip: 'Перейти к источнику',
                ),

                Expanded(
                  child: Text(
                    state.sourcePageTitle ?? 'Аудио Wiki',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Кнопка минимизации
                IconButton(
                  icon: Icon(
                    LucideIcons.minimize2,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha(179),
                    size: 18,
                  ),
                  onPressed: () {
                    SimpleAudioService.instance.minimizePlayer();
                  },
                  tooltip: 'Свернуть',
                ),
              ],
            ),
          ),

          // Основной плеер
          AudioPlayerWidget(
            position: state.position,
            duration: state.duration,
            isPlaying: state.isPlaying,
            onPlayPause: () {
              if (state.isPlaying) {
                SimpleAudioService.instance.pause();
              } else {
                SimpleAudioService.instance.playAudio(
                  fileId: state.currentFileId!,
                  audioUrl: state.audioUrl,
                  sourcePageId: state.sourcePageId,
                  sourcePageTitle: state.sourcePageTitle,
                );
              }
            },
            onSeekBackward: (seconds) {
              SimpleAudioService.instance.rewind();
            },
            onSeekForward: (seconds) {
              SimpleAudioService.instance.fastForward();
            },
            onStop: () {
              SimpleAudioService.instance.stop();
            },
            onSeek: (seconds) {
              SimpleAudioService.instance.seek(Duration(seconds: seconds));
            },
          ),
        ],
      ),
    );
  }

  /// Форматирует длительность в строку
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }
}
