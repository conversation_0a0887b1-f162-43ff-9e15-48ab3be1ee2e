import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';

/// Провайдер для состояния подключения к Supabase
final supabaseConnectionProvider = StreamProvider<bool>((ref) {
  final connectivityService = ref.read(connectivityServiceProvider);
  return connectivityService.supabaseStatusStream;
});

/// Ненавязчивый индикатор offline статуса
class OfflineIndicator extends ConsumerWidget {
  const OfflineIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionAsync = ref.watch(supabaseConnectionProvider);
    final warningColor = Colors.orange.shade600;

    return connectionAsync.when(
      data: (isConnected) {
        if (isConnected) {
          return const SizedBox.shrink(); // Скрываем индикатор когда онлайн
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: warningColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: warningColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.cloud_off_outlined, size: 16, color: warningColor),
              const SizedBox(width: 6),
              Text(
                'Offline',
                style: TextStyle(
                  fontSize: 12,
                  color: warningColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, _) => const SizedBox.shrink(),
    );
  }
}

/// Компактный индикатор offline статуса (только иконка)
class OfflineIndicatorCompact extends ConsumerWidget {
  const OfflineIndicatorCompact({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionAsync = ref.watch(supabaseConnectionProvider);
    final warningColor = Colors.orange.shade600;

    return connectionAsync.when(
      data: (isConnected) {
        if (isConnected) {
          return const SizedBox.shrink(); // Скрываем индикатор когда онлайн
        }

        return Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: warningColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: warningColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Icon(Icons.cloud_off_outlined, size: 16, color: warningColor),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, _) => const SizedBox.shrink(),
    );
  }
}
