import 'package:flutter/material.dart';

/// Универсальные кнопки приложения
/// Устраняет дублирование стилей и паттернов кнопок

/// Основная кнопка приложения
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final AppButtonStyle style;
  final AppButtonSize size;
  final bool isLoading;
  final EdgeInsetsGeometry? padding;

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.style = AppButtonStyle.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.padding,
  });

  const AppButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.padding,
  }) : style = AppButtonStyle.primary;

  const AppButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.padding,
  }) : style = AppButtonStyle.secondary;

  const AppButton.outline({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.padding,
  }) : style = AppButtonStyle.outline;

  const AppButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.padding,
  }) : style = AppButtonStyle.text;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final buttonStyle = _getButtonStyle(colorScheme);

    Widget child = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (isLoading) ...[
          SizedBox(
            width: _getIconSize(),
            height: _getIconSize(),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: _getContentColor(colorScheme),
            ),
          ),
          const SizedBox(width: 8),
        ] else if (icon != null) ...[
          Icon(icon, size: _getIconSize()),
          const SizedBox(width: 8),
        ],
        Text(text),
      ],
    );

    switch (style) {
      case AppButtonStyle.primary:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
      case AppButtonStyle.secondary:
        return FilledButton.tonal(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
      case AppButtonStyle.outline:
        return OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
      case AppButtonStyle.text:
        return TextButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
    }
  }

  EdgeInsetsGeometry _getPadding() {
    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 16);
    }
  }

  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return 16;
      case AppButtonSize.medium:
        return 18;
      case AppButtonSize.large:
        return 20;
    }
  }

  ButtonStyle _getButtonStyle(ColorScheme colorScheme) {
    return ButtonStyle(
      padding: WidgetStateProperty.all(padding ?? _getPadding()),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Color _getContentColor(ColorScheme colorScheme) {
    switch (style) {
      case AppButtonStyle.primary:
        return colorScheme.onPrimary;
      case AppButtonStyle.secondary:
        return colorScheme.onSecondaryContainer;
      case AppButtonStyle.outline:
        return colorScheme.primary;
      case AppButtonStyle.text:
        return colorScheme.primary;
    }
  }
}

/// Круглая кнопка-иконка
class AppIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String? tooltip;
  final AppIconButtonStyle style;
  final AppButtonSize size;
  final Color? color;

  const AppIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.style = AppIconButtonStyle.standard,
    this.size = AppButtonSize.medium,
    this.color,
  });

  const AppIconButton.filled({
    super.key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.size = AppButtonSize.medium,
    this.color,
  }) : style = AppIconButtonStyle.filled;

  const AppIconButton.outlined({
    super.key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.size = AppButtonSize.medium,
    this.color,
  }) : style = AppIconButtonStyle.outlined;

  @override
  Widget build(BuildContext context) {
    final iconSize = _getIconSize();

    Widget button;
    switch (style) {
      case AppIconButtonStyle.standard:
        button = IconButton(
          icon: Icon(icon, size: iconSize),
          onPressed: onPressed,
          tooltip: tooltip,
          color: color,
        );
        break;
      case AppIconButtonStyle.filled:
        button = IconButton.filled(
          icon: Icon(icon, size: iconSize),
          onPressed: onPressed,
          tooltip: tooltip,
          color: color,
        );
        break;
      case AppIconButtonStyle.outlined:
        button = IconButton.outlined(
          icon: Icon(icon, size: iconSize),
          onPressed: onPressed,
          tooltip: tooltip,
          color: color,
        );
        break;
    }

    return button;
  }

  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return 16;
      case AppButtonSize.medium:
        return 20;
      case AppButtonSize.large:
        return 24;
    }
  }
}

enum AppButtonStyle { primary, secondary, outline, text }

enum AppIconButtonStyle { standard, filled, outlined }

enum AppButtonSize { small, medium, large }
