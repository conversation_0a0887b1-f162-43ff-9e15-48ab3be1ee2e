import 'dart:async';
import 'dart:ui';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:luxury_app/core/services/cache_service.dart';
import 'package:luxury_app/shared/widgets/fullscreen_image_screen.dart';

/// Виджет для кэшированной загрузки изображений в markdown
class CachedImageWidget extends StatefulWidget {
  final String url;
  final VoidCallback? onLoaded;
  const CachedImageWidget({super.key, required this.url, this.onLoaded});

  @override
  State<CachedImageWidget> createState() => _CachedImageWidgetState();
}

class _CachedImageWidgetState extends State<CachedImageWidget> {
  double? width;
  double? height;
  Uint8List? imageBytes;
  bool loading = true;
  bool error = false;
  bool isAnimated = false;
  late final Dio _dio;

  @override
  void initState() {
    super.initState();
    _dio = Dio();
    _checkIfAnimated();
    _load();
  }

  @override
  void dispose() {
    _dio.close();
    super.dispose();
  }

  /// Проверяет, является ли изображение анимированным
  void _checkIfAnimated() {
    final url = widget.url.toLowerCase();
    // Проверяем по расширению
    if (url.contains('.gif') ||
        url.contains('.webp') ||
        url.contains('.apng')) {
      isAnimated = true;
      return;
    }

    // Проверяем по MIME-типу в URL (если есть)
    if (url.contains('image/gif') ||
        url.contains('image/webp') ||
        url.contains('image/apng')) {
      isAnimated = true;
      return;
    }

    // Для ссылок без расширения - предполагаем, что может быть анимированным
    // Это будет проверено при загрузке
    final uri = Uri.tryParse(widget.url);
    if (uri != null && !uri.path.contains('.')) {
      isAnimated = true; // Предполагаем анимированным для безопасности
    }
  }

  Future<void> _load() async {
    if (kIsWeb || isAnimated) {
      // Для web/PWA или анимированных изображений — используем прямую загрузку без кэширования
      try {
        final response = await _dio.get(
          widget.url,
          options: Options(responseType: ResponseType.bytes),
        );

        if (response.statusCode == 200) {
          // Проверяем MIME-тип ответа для уточнения анимации
          final contentType =
              response.headers.value('content-type')?.toLowerCase() ?? '';
          if (contentType.contains('gif') ||
              contentType.contains('webp') ||
              contentType.contains('apng')) {
            isAnimated = true;
          }

          imageBytes = Uint8List.fromList(response.data);
          final codec = await instantiateImageCodec(imageBytes!);
          final frameInfo = await codec.getNextFrame();
          width = frameInfo.image.width.toDouble();
          height = frameInfo.image.height.toDouble();
          if (!mounted) return;
          setState(() {
            loading = false;
          });
          widget.onLoaded?.call();
        } else {
          if (!mounted) return;
          setState(() {
            error = true;
            loading = false;
          });
        }
      } catch (e) {
        if (!mounted) return;
        setState(() {
          error = true;
          loading = false;
        });
      }
      return;
    }
    // Для остальных платформ — старая логика с файловым кэшированием
    await CacheService.instance.init();
    final size = CacheService.instance.getImageSize(widget.url);
    if (size != null) {
      width = size['w']?.toDouble();
      height = size['h']?.toDouble();
    }
    final file = await CacheService.instance.getCachedImageFile(widget.url);
    if (file != null && await file.exists()) {
      imageBytes = await file.readAsBytes();
      if (!mounted) return;
      setState(() {
        loading = false;
      });
      widget.onLoaded?.call();
      return;
    }
    try {
      // Получаем raw байты (поддержка анимаций)
      final response = await _dio.get(
        widget.url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        imageBytes = Uint8List.fromList(response.data);
        // Декодируем для получения размеров
        final codec = await instantiateImageCodec(imageBytes!);
        final frameInfo = await codec.getNextFrame();
        width = frameInfo.image.width.toDouble();
        height = frameInfo.image.height.toDouble();
        // Кешируем файл и размеры
        await CacheService.instance.saveImageFile(widget.url, imageBytes!);
        await CacheService.instance.saveImageSize(
          widget.url,
          width!.toInt(),
          height!.toInt(),
        );
        if (!mounted) return;
        setState(() {
          loading = false;
        });
        widget.onLoaded?.call();
      } else {
        if (!mounted) return;
        setState(() {
          error = true;
          loading = false;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        error = true;
        loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth = constraints.maxWidth;
        if (loading) {
          if (width != null && height != null) {
            final displayWidth = width! <= maxWidth ? width! : maxWidth;
            final displayHeight = displayWidth * height! / width!;
            return SizedBox(
              width: displayWidth,
              height: displayHeight,
              child: Container(
                color: Colors.grey[200],
                child: const Center(
                  child: CircularProgressIndicator(strokeWidth: 1),
                ),
              ),
            );
          }
          return SizedBox(
            width: maxWidth,
            height: 200,
            child: Container(
              color: Colors.grey[200],
              child: const Center(
                child: CircularProgressIndicator(strokeWidth: 1),
              ),
            ),
          );
        }
        if (error) {
          if (width != null && height != null) {
            final displayWidth = width! <= maxWidth ? width! : maxWidth;
            final displayHeight = displayWidth * height! / width!;
            return SizedBox(
              width: displayWidth,
              height: displayHeight,
              child: Container(
                color: Colors.grey[300],
                child: const Center(child: Icon(Icons.broken_image)),
              ),
            );
          }
          return SizedBox(
            width: maxWidth,
            height: 200,
            child: Container(
              color: Colors.grey[300],
              child: const Center(child: Icon(Icons.broken_image)),
            ),
          );
        }
        final displayWidth =
            (width != null && width! <= maxWidth) ? width! : maxWidth;
        final displayHeight =
            (width != null && height != null)
                ? displayWidth * height! / width!
                : null;
        final imgWidget =
            imageBytes != null
                ? (isAnimated
                    ? Image.network(
                      widget.url,
                      width: displayWidth,
                      height: displayHeight,
                      fit: BoxFit.contain,
                      gaplessPlayback: true,
                      errorBuilder: (context, error, stackTrace) {
                        // Fallback на Image.memory если network не работает
                        return Image.memory(
                          imageBytes!,
                          width: displayWidth,
                          height: displayHeight,
                          fit: BoxFit.contain,
                          gaplessPlayback: true,
                        );
                      },
                    )
                    : Image.memory(
                      imageBytes!,
                      width: displayWidth,
                      height: displayHeight,
                      fit: BoxFit.contain,
                      gaplessPlayback: true,
                    ))
                : const SizedBox.shrink();
        return GestureDetector(
          onTap:
              () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => FullscreenImageScreen(imageUrl: widget.url),
                ),
              ),
          child: InteractiveViewer(
            panEnabled: true,
            minScale: 1.0,
            maxScale: 4.0,
            child: imgWidget,
          ),
        );
      },
    );
  }
}
