import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/core/models/data_state.dart';
import 'package:luxury_app/shared/widgets/app_buttons.dart';

/// Унифицированные виджеты для отображения различных состояний приложения
/// Устраняет дублирование логики отображения ошибок, загрузки и пустых состояний

/// Виджет для отображения состояния загрузки
class LoadingStateWidget extends StatelessWidget {
  final String? message;
  final bool showMessage;

  const LoadingStateWidget({super.key, this.message, this.showMessage = false});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          if (showMessage && message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Виджет для отображения состояния ошибки
class ErrorStateWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;
  final String? retryButtonText;

  const ErrorStateWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.icon,
    this.retryButtonText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? LucideIcons.alertCircle,
              size: 48,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              AppButton.primary(
                text: retryButtonText ?? 'Повторить',
                onPressed: onRetry,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Виджет для отображения пустого состояния
class EmptyStateWidget extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? action;

  const EmptyStateWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null)
              Icon(
                icon,
                size: 56,
                color: theme.colorScheme.primary.withAlpha(
                  (theme.colorScheme.primary.a * 0.5).toInt(),
                ),
              ),
            const SizedBox(height: 16),
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(
                  (theme.colorScheme.onSurface.a * 0.7).toInt(),
                ),
              ),
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(
                    (theme.colorScheme.onSurface.a * 0.5).toInt(),
                  ),
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (action != null) ...[const SizedBox(height: 24), action!],
          ],
        ),
      ),
    );
  }
}

/// Упрощенный виджет для отображения состояний с данными
class DataStateWidget<T> extends StatelessWidget {
  final bool isLoading;
  final String? error;
  final T? data;
  final Widget Function(T data) dataBuilder;
  final VoidCallback? onRetry;
  final String? emptyMessage;

  const DataStateWidget({
    super.key,
    required this.isLoading,
    this.error,
    this.data,
    required this.dataBuilder,
    this.onRetry,
    this.emptyMessage,
  });

  @override
  Widget build(BuildContext context) {
    // Показываем загрузку только если нет данных
    if (isLoading && !_hasData(data)) {
      return const LoadingStateWidget();
    }

    // Показываем ошибку только если нет данных
    if (error != null && !_hasData(data)) {
      return ErrorStateWidget(message: error!, onRetry: onRetry);
    }

    // Показываем пустое состояние если нет данных
    if (!_hasData(data)) {
      return EmptyStateWidget(title: emptyMessage ?? 'Нет данных');
    }

    // Показываем данные
    return dataBuilder(data as T);
  }

  /// Проверяет наличие данных
  bool _hasData(T? data) {
    if (data == null) return false;
    if (data is List) return (data as List).isNotEmpty;
    if (data is Map) return (data as Map).isNotEmpty;
    if (data is String) return (data as String).isNotEmpty;
    return true;
  }
}

/// Билдер состояний для DataState
class StateBuilder<T> extends StatelessWidget {
  final T state;
  final Widget Function(BuildContext context) loadingBuilder;
  final Widget Function(BuildContext context, String error) errorBuilder;
  final Widget Function(BuildContext context) emptyBuilder;
  final Widget Function(BuildContext context, dynamic data) dataBuilder;

  const StateBuilder({
    super.key,
    required this.state,
    required this.loadingBuilder,
    required this.errorBuilder,
    required this.emptyBuilder,
    required this.dataBuilder,
  });

  @override
  Widget build(BuildContext context) {
    // Если состояние является DataState
    if (state is DataState) {
      final dataState = state as DataState;

      if (dataState.isLoading && dataState.data == null) {
        return loadingBuilder(context);
      }

      if (dataState.error != null && dataState.data == null) {
        return errorBuilder(context, dataState.error!);
      }

      if (dataState.data == null) {
        return emptyBuilder(context);
      }

      return dataBuilder(context, dataState.data);
    }

    // Fallback для других типов состояний
    return dataBuilder(context, state);
  }
}
