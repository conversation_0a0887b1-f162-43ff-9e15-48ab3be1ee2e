import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:photo_view/photo_view.dart';

class FullscreenImageScreen extends StatelessWidget {
  final String? imageUrl;
  final Uint8List? imageBytes;
  final String? heroTag;

  const FullscreenImageScreen({
    super.key,
    this.imageUrl,
    this.imageBytes,
    this.heroTag,
  }) : assert(imageUrl != null || imageBytes != null, 'Either imageUrl or imageBytes must be provided');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      body: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Center(
          child: PhotoView(
            imageProvider: _getImageProvider(),
            backgroundDecoration: const BoxDecoration(color: Colors.black),
            minScale: PhotoViewComputedScale.contained,
            maxScale: PhotoViewComputedScale.covered * 3.0,
            initialScale: PhotoViewComputedScale.contained,
            heroAttributes: PhotoViewHeroAttributes(
              tag: heroTag ?? (imageUrl ?? 'image_${imageBytes.hashCode}'),
            ),
            loadingBuilder: (context, event) {
              return const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.broken_image,
                      color: Colors.white,
                      size: 64,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Ошибка загрузки изображения',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  ImageProvider _getImageProvider() {
    if (imageBytes != null) {
      return MemoryImage(imageBytes!);
    } else if (imageUrl != null) {
      return NetworkImage(imageUrl!);
    } else {
      throw ArgumentError('Either imageUrl or imageBytes must be provided');
    }
  }
}
