import 'dart:async';
import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:luxury_app/core/services/cache_service.dart';
import 'package:luxury_app/core/services/platform_services.dart';

/// Единый сервис для рендеринга Mermaid диаграмм
class MermaidService {
  static CacheService get _cacheService => CacheService.instance;

  /// Очистка кода от markdown блоков
  static String _cleanMermaidCode(String code) {
    String cleanCode = code.trim();

    // Универсальная очистка: извлекаем только содержимое между ```mermaid ... ```
    final mermaidBlock = RegExp(
      r'```mermaid\s*([\s\S]*?)\s*```',
      multiLine: true,
    );
    final match = mermaidBlock.firstMatch(cleanCode);
    if (match != null) {
      cleanCode = match.group(1)!.trim();
    } else {
      // Если передали просто markdown-блок без "mermaid"
      final genericBlock = RegExp(r'```\s*([\s\S]*?)\s*```', multiLine: true);
      final genericMatch = genericBlock.firstMatch(cleanCode);
      if (genericMatch != null) {
        cleanCode = genericMatch.group(1)!.trim();
      }
    }

    return cleanCode;
  }

  /// Рендеринг Mermaid диаграммы в SVG
  static Future<String> render(String code) async {
    final cleanCode = _cleanMermaidCode(code);
    final key = sha256.convert(utf8.encode(cleanCode)).toString();

    // Проверяем кэш
    final cachedSvg = _cacheService.getMermaidSvg(key);
    if (cachedSvg != null) {
      return cachedSvg;
    }

    // Делегируем рендеринг платформо-специфичной реализации
    final svg = await PlatformServices.mermaidService.render(cleanCode);
    await _cacheService.saveMermaidSvg(key, svg);
    return svg;
  }

  /// Очистка ресурсов
  static Future<void> dispose() async {
    await PlatformServices.mermaidService.dispose();
    await _cacheService.clearMermaidCache();
  }
}
