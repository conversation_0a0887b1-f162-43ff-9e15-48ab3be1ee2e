import 'package:intl/intl.dart';

/// Утилиты для форматирования даты и времени
class DateFormatter {
  /// Форматирует дату и время в европейском формате (день.месяц.год часы:минуты)
  ///
  /// Пример: 28.03.2025 14:30
  static String formatDateTime(DateTime dateTime) {
    // Создаем форматтер с нужным форматом (день.месяц.год часы:минуты)
    final formatter = DateFormat('dd.MM.yyyy HH:mm');

    // Форматируем дату
    return formatter.format(dateTime);
  }

  /// Форматирует время в относительном формате (только что, минуты назад, часы назад, сегодня, вчера, дата)
  ///
  /// Примеры:
  /// - Только что (< 1 минуты)
  /// - 5 мин. назад
  /// - 2 ч. назад
  /// - Сегодня 14:30
  /// - Вчера 14:30
  /// - 25.12.2024 14:30
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    // Только что (меньше минуты)
    if (difference.inMinutes < 1) {
      return 'Только что';
    }

    // Минуты назад (меньше часа)
    if (difference.inHours < 1) {
      return '${difference.inMinutes} мин. назад';
    }

    // Часы назад (меньше суток, но сегодня)
    if (difference.inDays < 1) {
      return '${difference.inHours} ч. назад';
    }

    // Сегодня (если был сегодня, но больше часа назад)
    final today = DateTime(now.year, now.month, now.day);
    final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (dateOnly == today) {
      final timeFormatter = DateFormat('HH:mm');
      return 'Сегодня ${timeFormatter.format(dateTime)}';
    }

    // Вчера
    final yesterday = today.subtract(const Duration(days: 1));
    if (dateOnly == yesterday) {
      final timeFormatter = DateFormat('HH:mm');
      return 'Вчера ${timeFormatter.format(dateTime)}';
    }

    // Дата и время для более старых записей
    final formatter = DateFormat('dd.MM.yyyy HH:mm');
    return formatter.format(dateTime);
  }
}
