import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';

/// Виджет для централизованного запуска конфетти сверху по центру экрана
/// Вспомогательный глобальный метод для праздничного взрыва конфетти поверх всего UI
void showConfettiAnywhere(BuildContext context) {
  final controller = ConfettiController(duration: const Duration(seconds: 1));
  late OverlayEntry overlay;
  overlay = OverlayEntry(
    builder:
        (ctx) => Positioned.fill(
          child: IgnorePointer(
            child: _FullScreenConfetti(
              controller: controller,
              onCompleted: () {
                controller.dispose();
                overlay.remove();
              },
            ),
          ),
        ),
  );
  Overlay.of(context, rootOverlay: true).insert(overlay);
  controller.play();
}

class _FullScreenConfetti extends StatefulWidget {
  final ConfettiController controller;
  final VoidCallback onCompleted;
  const _FullScreenConfetti({
    required this.controller,
    required this.onCompleted,
  });

  @override
  State<_FullScreenConfetti> createState() => _FullScreenConfettiState();
}

class _FullScreenConfettiState extends State<_FullScreenConfetti> {
  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onStatusChanged);
  }

  void _onStatusChanged() {
    if (widget.controller.state == ConfettiControllerState.stopped) {
      widget.controller.removeListener(_onStatusChanged);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onCompleted();
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Верхний залп
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: widget.controller,
            blastDirectionality: BlastDirectionality.explosive,
            blastDirection: 3.14 / 2, // вниз
            emissionFrequency: 0.4, // интенсивный залп
            numberOfParticles: 150, // больше частиц
            maxBlastForce: 8, // сильнее взрыв
            minBlastForce: 4,
            gravity: 0.03, // медленнее падают
            shouldLoop: false,
            colors: const [
              Color(0xFFFFC107), // жёлтый
              Color(0xFF7C4DFF), // фиолетовый
              Color(0xFF2196F3), // синий
              Color(0xFFE91E63), // розовый
              Color(0xFF4CAF50), // зелёный
              Color(0xFFFF5722), // оранжевый
              Color(0xFF00BCD4), // бирюзовый
              Color(0xFFFFEB3B), // лимонный
              Color(0xFFFFFFFF), // белый
              Color(0xFFFF9800), // янтарный
              Color(0xFF9C27B0), // пурпурный
              Color(0xFF3F51B5), // индиго
            ],
            createParticlePath: _drawConfettiShape,
            particleDrag: 0.01, // меньше сопротивления
            minimumSize: const Size(8, 6),
            maximumSize: const Size(32, 24),
          ),
        ),
      ],
    );
  }
}

/// Разнообразные формы конфетти
Path _drawConfettiShape(Size size) {
  final random = DateTime.now().microsecondsSinceEpoch % 2;
  final path = Path();

  switch (random) {
    case 0: // Овал
      path.addOval(
        Rect.fromCenter(
          center: Offset(size.width / 2, size.height / 2),
          width: size.width * 0.9,
          height: size.height * 0.5,
        ),
      );
    case 1: // Прямоугольник
      path.addRRect(
        RRect.fromRectAndRadius(
          Rect.fromCenter(
            center: Offset(size.width / 2, size.height / 2),
            width: size.width * 0.8,
            height: size.height * 0.4,
          ),
          Radius.circular(size.width * 0.1),
        ),
      );
  }

  return path;
}
