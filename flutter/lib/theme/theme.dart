import 'package:flutter/material.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/constants/typography.dart';

/// Цветовые схемы приложения
class AppColors {
  // Приватный конструктор, чтобы запретить создание экземпляров
  const AppColors._();

  // Брендовые цвета (ТОЛЬКО для логотипа)
  static const Color brandPurple = Color(0xFF8B0D6A);
  static const Color brandGold = Color(0xFFFFED00);

  // Цвета для просмотрщика изображений и видео
  static const Color viewerBackground = Color(
    0xFF000000,
  ); // Черный фон для просмотрщика
  static const Color viewerText = Color(
    0xFFFFFFFF,
  ); // Белый текст для просмотрщика
  static const Color viewerTextSecondary = Color(
    0xB3FFFFFF,
  ); // Белый текст с прозрачностью (~70%)
  static const Color viewerIcon = Color(
    0xFFFFFFFF,
  ); // Белые иконки для просмотрщика
  static const Color viewerIconSecondary = Color(
    0xB3FFFFFF,
  ); // Белые иконки с прозрачностью (~70%)

  // Оттенки серого для выделения (используйте вместо цветных подложек)
  static const Color selectedGrayLight = Color(
    0xFFE0E0E0,
  ); // Для выделения в светлой теме
  static const Color selectedGrayDark = Color(
    0xFF333333,
  ); // Для выделения в темной теме

  // === Светлая тема: эффект белой бумаги ===
  static const Color backgroundLight = Color(0xFFF7F6F3); // Бумажный белый
  // Базовый белый цвет бумаги
  static const Color paperWhite = Color(0xFFFFFFFF);

  // Немного теплый белый оттенок поверхностей (имитация бумаги)
  static const Color surfaceLight = Color(0xFFFAFAFA);

  // Черный текст на белой бумаге с высоким контрастом
  static const Color onSurfaceLight = Color(0xFF121212);
  // Светлый текст на тёмном фоне
  static const Color onSurfaceDark = Color(0xFFF3F3F3);

  // Градации серого для контейнеров
  static const Color surfaceContainerLight = Color(
    0xFFF2F2F2,
  ); // Легкая текстура
  static const Color surfaceContainerLowLight = Color(
    0xFFF8F8F8,
  ); // Едва заметный оттенок
  static const Color surfaceContainerHighLight = Color(
    0xFFE6E6E6,
  ); // Более выраженная текстура

  // Тень для эффекта приподнятости над бумагой
  static const Color shadowLight = Color(0x26000000); // 15% прозрачности

  // === Темная тема: основной фон и контейнеры ===
  static const Color surfaceContainerDark = Color(
    0xFF181818,
  ); // Более глубокий фон для тёмной темы (Material 3)
  static const Color surfaceContainerLowDark = Color(
    0xFF1D1D1D,
  ); // Едва заметный оттенок
  static const Color surfaceContainerHighDark = Color(
    0xFF2A2A2A,
  ); // Более выраженная текстура

  // Тень для эффекта приподнятости над черной бумагой
  static const Color shadowDark = Color(0x3D000000); // 24% прозрачности
}

/// Генерирует базовую тему в зависимости от режима (тёмный/светлый)
ThemeData _getBaseTheme(BuildContext context, bool isDark) {
  // Создаем посевной цвет для генерации гармоничной цветовой схемы
  final seedColor =
      isDark ? AppColors.surfaceContainerDark : const Color(0xFF64B5F6);

  // Базовые цвета для бумажной темы
  final backgroundColor =
      isDark ? AppColors.surfaceContainerDark : AppColors.backgroundLight;
  final surfaceColor =
      isDark
          ? AppColors.surfaceContainerDark
          : AppColors.surfaceLight; // для карточек, но не для drawer

  final onSurfaceColor =
      isDark ? AppColors.onSurfaceDark : AppColors.onSurfaceLight;
  final surfaceContainerColor =
      isDark ? AppColors.surfaceContainerDark : AppColors.surfaceContainerLight;
  final surfaceContainerHighColor =
      isDark
          ? AppColors.surfaceContainerHighDark
          : AppColors.surfaceContainerHighLight;
  final shadowColor = isDark ? AppColors.shadowDark : AppColors.shadowLight;

  // Генерируем цветовую схему Material 3 на основе посевного цвета
  final colorScheme = ColorScheme.fromSeed(
    seedColor: seedColor,
    brightness: isDark ? Brightness.dark : Brightness.light,
  ).copyWith(
    // Переопределяем ключевые цвета для эффекта бумаги
    surface: backgroundColor,
    surfaceContainerHighest: surfaceContainerColor,
    onSurface: onSurfaceColor,
  );

  return ThemeData(
    useMaterial3: true,
    colorScheme: colorScheme,
    brightness: isDark ? Brightness.dark : Brightness.light,
    scaffoldBackgroundColor:
        isDark ? AppColors.surfaceContainerDark : AppColors.backgroundLight,
    canvasColor: backgroundColor,
    cardColor:
        isDark
            ? AppColors.surfaceContainerDark
            : AppColors.surfaceContainerLight,
    dividerColor: colorScheme.outline.withAlpha(30),
    shadowColor: shadowColor,
    fontFamily: AppTypography.fontFamilyPrimary,

    // Эффект приподнятости (отражает бумажный характер элементов)
    cardTheme: CardThemeData(
      color: surfaceColor,
      surfaceTintColor: Colors.transparent,
      elevation: 0.5,
      shadowColor: shadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
      ),
    ),

    textSelectionTheme: TextSelectionThemeData(
      cursorColor: colorScheme.primary,
      selectionColor: colorScheme.primary.withAlpha(75),
      selectionHandleColor: colorScheme.primary,
    ),

    appBarTheme: AppBarTheme(
      backgroundColor:
          isDark ? AppColors.surfaceContainerDark : AppColors.backgroundLight,
      foregroundColor: onSurfaceColor,
      elevation: 0,
      shadowColor: shadowColor,
      titleTextStyle: TextStyle(
        fontFamily: AppTypography.fontFamilyPrimary,
        color: onSurfaceColor,
        fontSize: AppTypography.titleMedium,
        fontWeight: AppTypography.weightBold,
        letterSpacing: AppTypography.letterSpacingTitle,
        height: AppTypography.lineHeightTitle,
      ),
      iconTheme: IconThemeData(color: onSurfaceColor, size: AppSizes.iconSize),
      centerTitle: true,
      surfaceTintColor: Colors.transparent,
      shape: Border(
        bottom: BorderSide(
          color: shadowColor.withAlpha((0.1 * 255).round()),
          width: 0.5,
        ),
      ),
    ),

    dialogTheme: DialogThemeData(
      backgroundColor: surfaceContainerHighColor,
      elevation: 3,
      shadowColor: shadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
      ),
      surfaceTintColor: Colors.transparent,
    ),

    drawerTheme: DrawerThemeData(
      backgroundColor:
          isDark ? AppColors.surfaceContainerDark : AppColors.backgroundLight,
      elevation: 0, // Убираем elevation для desktop версии
      shadowColor: shadowColor,
      surfaceTintColor: Colors.transparent,
      shape: const RoundedRectangleBorder(), // Без скругления для desktop
    ),

    // Material 3 текстовая тема - оптимизирована для Noto Sans
    textTheme: TextTheme(
      displayLarge: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.displayLarge,
        fontWeight: AppTypography.weightLight,
        letterSpacing: AppTypography.letterSpacingDisplay,
        height: AppTypography.lineHeightDisplay,
      ),
      displayMedium: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.displayMedium,
        fontWeight: AppTypography.weightLight,
        letterSpacing: AppTypography.letterSpacingDisplay,
        height: AppTypography.lineHeightDisplay,
      ),
      displaySmall: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.displaySmall,
        fontWeight: AppTypography.weightRegular,
        letterSpacing: AppTypography.letterSpacingDisplay,
        height: AppTypography.lineHeightDisplay,
      ),
      headlineLarge: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.headlineLarge,
        fontWeight: AppTypography.weightRegular,
        letterSpacing: AppTypography.letterSpacingHeadline,
        height: AppTypography.lineHeightHeadline,
      ),
      headlineMedium: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.headlineMedium,
        fontWeight: AppTypography.weightRegular,
        letterSpacing: AppTypography.letterSpacingHeadline,
        height: AppTypography.lineHeightHeadline,
      ),
      headlineSmall: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.headlineSmall,
        fontWeight: AppTypography.weightSemiBold,
        letterSpacing: AppTypography.letterSpacingHeadline,
        height: AppTypography.lineHeightHeadline,
      ),
      titleLarge: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.titleLarge,
        fontWeight: AppTypography.weightSemiBold,
        letterSpacing: AppTypography.letterSpacingTitle,
        height: AppTypography.lineHeightTitle,
      ),
      titleMedium: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.titleMedium,
        fontWeight: AppTypography.weightSemiBold,
        letterSpacing: AppTypography.letterSpacingTitle,
        height: AppTypography.lineHeightTitle,
      ),
      titleSmall: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.titleSmall,
        fontWeight: AppTypography.weightSemiBold,
        letterSpacing: AppTypography.letterSpacingTitle,
        height: AppTypography.lineHeightTitle,
      ),
      bodyLarge: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.bodyLarge,
        fontWeight: AppTypography.weightRegular,
        letterSpacing: AppTypography.letterSpacingBody,
        height: AppTypography.lineHeightBody,
      ),
      bodyMedium: TextStyle(
        color: colorScheme.onSurfaceVariant,
        fontSize: AppTypography.bodyMedium,
        fontWeight: AppTypography.weightRegular,
        letterSpacing: AppTypography.letterSpacingBody,
        height: AppTypography.lineHeightBody,
      ),
      bodySmall: TextStyle(
        color: colorScheme.onSurfaceVariant,
        fontSize: AppTypography.bodySmall,
        fontWeight: AppTypography.weightRegular,
        letterSpacing: AppTypography.letterSpacingBody,
        height: AppTypography.lineHeightBody,
      ),
      labelLarge: TextStyle(
        color: colorScheme.onSurface,
        fontSize: AppTypography.labelLarge,
        fontWeight: AppTypography.weightSemiBold,
        letterSpacing: AppTypography.letterSpacingLabel,
        height: AppTypography.lineHeightCompact,
      ),
      labelMedium: TextStyle(
        color: colorScheme.onSurfaceVariant,
        fontSize: AppTypography.labelMedium,
        fontWeight: AppTypography.weightSemiBold,
        letterSpacing: AppTypography.letterSpacingLabel,
        height: AppTypography.lineHeightCompact,
      ),
      labelSmall: TextStyle(
        color: colorScheme.onSurfaceVariant,
        fontSize: AppTypography.labelSmall,
        fontWeight: AppTypography.weightSemiBold,
        letterSpacing: AppTypography.letterSpacingLabel,
        height: AppTypography.lineHeightCompact,
      ),
    ),

    // Material 3 стили кнопок
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return colorScheme.onSurface.withAlpha(30);
          }
          return colorScheme.primary;
        }),
        foregroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return colorScheme.onSurface.withAlpha(95);
          }
          return colorScheme.onPrimary;
        }),
        elevation: WidgetStateProperty.all(1),
        shadowColor: WidgetStateProperty.all(shadowColor),
        padding: WidgetStateProperty.all(
          EdgeInsets.symmetric(
            horizontal: AppSizes.paddingM * 2,
            vertical: AppSizes.paddingM,
          ),
        ),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
        ),
      ),
    ),

    // Material 3 стили полей ввода с эффектом бумаги
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: surfaceContainerColor,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        borderSide: BorderSide(color: colorScheme.primary, width: 1),
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: AppSizes.paddingM,
        vertical: AppSizes.paddingS,
      ),
      // Эффект легкой тени для полей ввода
      isDense: true,
    ),

    // Настройки листов и карточек
    listTileTheme: ListTileThemeData(
      tileColor: surfaceColor,
      selectedTileColor: surfaceContainerColor,
      contentPadding: EdgeInsets.symmetric(
        horizontal: AppSizes.paddingM,
        vertical: AppSizes.paddingS,
      ),
    ),
  );
}

/// Возвращает светлую тему
ThemeData getLightTheme(BuildContext context) => _getBaseTheme(context, false);

/// Возвращает темную тему
ThemeData getDarkTheme(BuildContext context) => _getBaseTheme(context, true);

/// Возвращает текущую тему в зависимости от выбора пользователя
ThemeData getCurrentTheme(BuildContext context, ThemeChoice choice) {
  if (choice == ThemeChoice.system) {
    final brightness = MediaQuery.of(context).platformBrightness;
    return brightness == Brightness.light
        ? getLightTheme(context)
        : getDarkTheme(context);
  }

  return choice == ThemeChoice.light
      ? getLightTheme(context)
      : getDarkTheme(context);
}

/// Расширения для ColorScheme для поддержки Material 3
extension ColorSchemeExtensions on ColorScheme {
  // Поверхности контейнеров (Material 3)
  Color get surfaceContainerLowest =>
      brightness == Brightness.light
          ? const Color(0xFFFFFFFF) // Белый для светлой темы
          : const Color(0xFF0D0E11); // Очень темный для темной темы

  Color get surfaceContainerLow =>
      brightness == Brightness.light
          ? const Color(0xFFF7F2FA) // Светло-фиолетовый для светлой темы
          : const Color(0xFF1D1B20); // Темно-фиолетовый для темной темы

  Color get surfaceContainer =>
      brightness == Brightness.light
          ? const Color(0xFFF3EDF7) // Светло-фиолетовый для светлой темы
          : const Color(0xFF211F26); // Темно-фиолетовый для темной темы

  Color get surfaceContainerHigh =>
      brightness == Brightness.light
          ? const Color(0xFFECE6F0) // Светло-фиолетовый для светлой темы
          : const Color(0xFF2B2930); // Темно-фиолетовый для темной темы

  Color get surfaceContainerHighest =>
      brightness == Brightness.light
          ? const Color(0xFFE6E0E9) // Светло-фиолетовый для светлой темы
          : const Color(0xFF36343B); // Темно-фиолетовый для темной темы

  // Дополнительные цвета для контента
  Color get surfaceDim =>
      brightness == Brightness.light
          ? const Color(0xFFDED8E1) // Приглушенный фиолетовый для светлой темы
          : const Color(0xFF141218); // Очень темный фиолетовый для темной темы

  Color get surfaceBright =>
      brightness == Brightness.light
          ? const Color(0xFFFCF8FF) // Яркий фиолетовый для светлой темы
          : const Color(0xFF3B3840); // Темно-фиолетовый для темной темы

  // Цвета для границ
  Color get outlineVariant =>
      brightness == Brightness.light
          ? const Color(0xFFCAC4D0) // Светло-серый для светлой темы
          : const Color(0xFF49454F); // Темно-серый для темной темы
}

/// Варианты тем приложения
enum ThemeChoice {
  light,
  dark,
  system;

  /// Получить ThemeMode на основе выбранной темы
  ThemeMode toThemeMode() {
    switch (this) {
      case ThemeChoice.light:
        return ThemeMode.light;
      case ThemeChoice.dark:
        return ThemeMode.dark;
      case ThemeChoice.system:
        return ThemeMode.system;
    }
  }
}

/// Ключи для хранения настроек темы
class ThemeConstants {
  // Приватный конструктор для запрета создания экземпляров
  const ThemeConstants._();

  /// Ключ для хранения выбора темы в SharedPreferences
  static const String themeChoiceKey = 'themeChoice';
}
