import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/core/services/cache_service.dart';

/// Провайдер для управления состоянием темы
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeMode>((ref) {
  return ThemeNotifier();
});

/// Нотификатор для управления состоянием темы
class ThemeNotifier extends StateNotifier<ThemeMode> {
  ThemeNotifier() : super(ThemeMode.system) {
    _loadSavedTheme();
  }

  /// Загрузка сохраненной темы
  Future<void> _loadSavedTheme() async {
    try {
      final cache = CacheService.instance;
      await cache.init();
      final modeStr = cache.getThemeMode();
      ThemeMode mode = ThemeMode.system;
      if (modeStr == 'light') mode = ThemeMode.light;
      if (modeStr == 'dark') mode = ThemeMode.dark;
      state = mode;
    } catch (e) {
      // При ошибке используем системную тему
      state = ThemeMode.system;
    }
  }

  /// Изменение темы
  Future<void> changeTheme(ThemeMode themeMode) async {
    try {
      final cache = CacheService.instance;
      await cache.init();
      String modeStr = 'system';
      if (themeMode == ThemeMode.light) modeStr = 'light';
      if (themeMode == ThemeMode.dark) modeStr = 'dark';
      await cache.saveThemeMode(modeStr);
      state = themeMode;
    } catch (e) {
      // При ошибке сохранения все равно обновляем состояние
      state = themeMode;
    }
  }

  /// Асинхронный фабричный метод для создания экземпляра ThemeNotifier
  static Future<ThemeNotifier> create() async {
    try {
      return ThemeNotifier();
    } catch (e) {
      rethrow;
    }
  }
}
