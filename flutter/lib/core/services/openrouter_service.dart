import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:luxury_app/features/ai_chat/data/ai_message.dart';
import 'package:luxury_app/shared/constants/ai_constants.dart';

/// Сервис для прямой интеграции с OpenRouter API
class OpenRouterService {
  final Dio _dio;

  OpenRouterService()
    : _dio = Dio(
        BaseOptions(
          baseUrl: AIConstants.openRouterBaseUrl,
          headers: {
            'Authorization': 'Bearer ${AIConstants.openRouterApiKey}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://luxury-app.com',
            'X-Title': 'Luxury Sound App',
          },
        ),
      );

  /// Генерирует простой ответ от AI
  Future<String> generateResponse({
    required List<AIMessage> messages,
    required String userMessage,
    List<Map<String, dynamic>>? attachments,
  }) async {
    try {
      final conversation = _prepareMessages(messages, userMessage, attachments);

      final requestData = {
        'model': AIConstants.llmModel,
        'messages': conversation,
        'stream': false,
        'temperature': 0.7,
        'max_tokens': 4000,
      };

      final response = await _dio.post('/chat/completions', data: requestData);

      if (response.data['choices'] != null &&
          response.data['choices'].isNotEmpty) {
        return response.data['choices'][0]['message']['content'] ?? '';
      }

      return 'Извините, не удалось получить ответ от AI';
    } catch (e) {
      if (kDebugMode) {
        log('❌ OpenRouter API Error: $e');
      }
      return 'Произошла ошибка при обращении к AI: ${e.toString()}';
    }
  }

  /// Генерирует потоковый ответ от AI
  Stream<Map<String, dynamic>> generateStreamingResponse({
    required List<AIMessage> messages,
    required String userMessage,
    List<Map<String, dynamic>>? attachments,
  }) async* {
    try {
      if (kDebugMode) {
        log('🚀 [OpenRouter] Начинаем потоковый запрос к OpenRouter');
        log('🚀 [OpenRouter] Модель: ${AIConstants.llmModel}');
        log('🚀 [OpenRouter] Сообщение: $userMessage');
      }

      final conversation = _prepareMessages(messages, userMessage, attachments);

      final requestData = {
        'model': AIConstants.llmModel,
        'messages': conversation,
        'stream': true,
        'temperature': 0.7,
        'max_tokens': 4000,
      };

      final response = await _dio.post<ResponseBody>(
        '/chat/completions',
        data: requestData,
        options: Options(
          responseType: ResponseType.stream,
          headers: {'Accept': 'text/event-stream'},
        ),
      );

      final stream = response.data!.stream;
      String buffer = '';
      String accumulatedContent = '';

      await for (final chunk in stream) {
        final chunkStr = utf8.decode(chunk);
        buffer += chunkStr;

        // Обрабатываем SSE события
        final lines = buffer.split('\n');
        buffer = lines.isNotEmpty ? lines.removeLast() : '';

        for (final line in lines) {
          if (line.startsWith('data: ')) {
            final eventData = line.substring(6).trim();

            if (eventData == '[DONE]') {
              if (kDebugMode) {
                log(
                  '✅ [OpenRouter] Поток завершен. Всего символов: ${accumulatedContent.length}',
                );
              }
              // Отправляем финальное событие
              yield {
                'type': 'done',
                'data': {'content': accumulatedContent},
              };
              return;
            }

            if (eventData.isNotEmpty) {
              try {
                final data = jsonDecode(eventData);

                if (data['choices'] != null && data['choices'].isNotEmpty) {
                  final choice = data['choices'][0];
                  final delta = choice['delta'];

                  if (delta != null && delta['content'] != null) {
                    final content = delta['content'] as String;
                    accumulatedContent += content;

                    if (kDebugMode && content.isNotEmpty) {
                      log(
                        '📝 [OpenRouter] Получен чанк: "${content.replaceAll('\n', '\\n')}"',
                      );
                    }

                    yield {
                      'type': 'completion',
                      'data': {'content': content},
                    };
                  }
                }
              } catch (e) {
                if (kDebugMode) {
                  log('❌ [OpenRouter] Error parsing SSE data: $e');
                  log('❌ [OpenRouter] Problematic data: $eventData');
                }
              }
            }
          }
        }
      }

      // Если дошли до сюда без [DONE], отправляем финальное событие
      if (accumulatedContent.isNotEmpty) {
        if (kDebugMode) {
          log(
            '⚠️ [OpenRouter] Поток завершен без [DONE]. Отправляем финальное событие',
          );
        }
        yield {
          'type': 'done',
          'data': {'content': accumulatedContent},
        };
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [OpenRouter] Streaming Error: $e');
      }
      yield {
        'type': 'error',
        'data': {
          'error': e.toString(),
          'message': 'Произошла ошибка при генерации потокового ответа',
        },
      };
    }
  }

  /// Подготавливает сообщения для отправки в API
  List<Map<String, dynamic>> _prepareMessages(
    List<AIMessage> messages,
    String userMessage,
    List<Map<String, dynamic>>? attachments,
  ) {
    final conversation = <Map<String, dynamic>>[];

    // Добавляем системный промпт
    conversation.add({'role': 'system', 'content': AIConstants.systemPrompt});

    // Добавляем историю сообщений (последние 20)
    final recentMessages = messages.take(20).toList();
    for (final message in recentMessages) {
      conversation.add({'role': message.role, 'content': message.content});
    }

    // Добавляем текущее сообщение пользователя
    final userContent = <Map<String, dynamic>>[];

    // Добавляем текст
    userContent.add({'type': 'text', 'text': userMessage});

    // Добавляем вложения (изображения) - модель поддерживает vision
    if (attachments != null) {
      for (final attachment in attachments) {
        if (attachment['file_type'] == 'image') {
          userContent.add({
            'type': 'image_url',
            'image_url': {'url': attachment['file_path']},
          });
        }
      }
    }

    conversation.add({
      'role': 'user',
      'content': userContent.length == 1 ? userMessage : userContent,
    });

    return conversation;
  }

  /// Освобождает ресурсы
  void dispose() {
    _dio.close();
  }
}
