import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:luxury_app/core/mixins/logger_mixin.dart';
// Mermaid Service Imports
import 'package:luxury_app/shared/widgets/base_mermaid_service.dart';
import 'package:luxury_app/shared/widgets/mermaid_service_io.dart';

class PlatformServices with LoggerMixin {
  PlatformServices._(); // Private constructor

  static BaseMermaidService get mermaidService {
    if (kIsWeb) {
      // Веб-версия не поддерживается, возвращаем базовую заглушку
      return _WebMermaidServiceStub();
    } else {
      return NativeMermaidService();
    }
  }
}

/// Простая заглушка для веб-версии
class _WebMermaidServiceStub implements BaseMermaidService {
  @override
  Future<String> render(String cleanCode) async {
    return '<div>Mermaid диаграммы не поддерживаются в веб-версии</div>';
  }

  @override
  Future<void> dispose() async {
    // Нет ресурсов для очистки
  }
}
