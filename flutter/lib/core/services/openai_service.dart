import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:luxury_app/shared/constants/ai_constants.dart';

/// Сервис для интеграции с OpenAI API (транскрибация аудио)
class OpenAIService {
  final Dio _dio;

  OpenAIService()
    : _dio = Dio(
        BaseOptions(
          baseUrl: AIConstants.openAIBaseUrl,
          headers: {'Authorization': 'Bearer ${AIConstants.openAIApiKey}'},
        ),
      );

  /// Транскрибирует аудио файл
  Future<String> transcribeAudio(File audioFile) async {
    final audioBytes = await audioFile.readAsBytes();
    final filename = audioFile.path.split('/').last;
    return transcribeAudioFromBytes(audioBytes, filename);
  }

  /// Транскрибирует аудио из байтов
  Future<String> transcribeAudioFromBytes(
    List<int> audioBytes,
    String filename,
  ) async {
    try {
      if (kDebugMode) {
        log('🎤 Начинаем транскрибацию аудио: $filename');
      }

      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(audioBytes, filename: filename),
        'model': AIConstants.transcribeModel,
        'response_format': 'text',
        'language': 'ru', // Приоритет русскому языку
      });

      final response = await _dio.post('/audio/transcriptions', data: formData);

      if (response.data is String) {
        final transcription = response.data as String;

        if (kDebugMode) {
          log('✅ Транскрибация завершена: ${transcription.length} символов');
        }

        return transcription;
      } else if (response.data is Map && response.data['text'] != null) {
        final transcription = response.data['text'] as String;

        if (kDebugMode) {
          log('✅ Транскрибация завершена: ${transcription.length} символов');
        }

        return transcription;
      }

      return 'Не удалось получить транскрибацию';
    } catch (e) {
      if (kDebugMode) {
        log('❌ OpenAI Transcription Error: $e');
        if (e is DioException) {
          log('❌ Response data: ${e.response?.data}');
          log('❌ Status code: ${e.response?.statusCode}');
          log('❌ Headers: ${e.response?.headers}');
        }
      }
      return 'Произошла ошибка при транскрибации: ${e.toString()}';
    }
  }

  /// Проверяет, поддерживается ли формат аудио файла
  bool isSupportedAudioFormat(String filename) {
    final supportedFormats = [
      '.mp3',
      '.mp4',
      '.mpeg',
      '.mpga',
      '.m4a',
      '.wav',
      '.webm',
      '.ogg',
      '.flac',
      '.aac',
    ];

    final extension = filename.toLowerCase().split('.').last;
    return supportedFormats.any((format) => format.endsWith(extension));
  }

  /// Получает максимальный размер файла для транскрибации (25 MB)
  int get maxFileSize => 25 * 1024 * 1024; // 25 MB в байтах

  /// Освобождает ресурсы
  void dispose() {
    _dio.close();
  }
}
