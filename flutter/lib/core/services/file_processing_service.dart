import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:heif_converter/heif_converter.dart';
import 'package:image/image.dart' as img;
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

/// Модель для обработанного файла
class ProcessedFile {
  final String filename;
  final Uint8List bytes;

  ProcessedFile({required this.filename, required this.bytes});
}

class FileProcessingService with LoggerMixin {
  Future<ProcessedFile> processFile(
    String filename,
    Uint8List bytes,
    String type,
  ) async {
    // Обрабатываем HEIC/HEIF конвертацию
    final heicProcessed = await _handleHeicConversion(filename, bytes, type);

    // Сжимаем изображения
    if (type == 'image') {
      final compressed = await _compressImage(
        heicProcessed.bytes,
        heicProcessed.filename,
      );
      return ProcessedFile(filename: heicProcessed.filename, bytes: compressed);
    }

    return heicProcessed;
  }

  Future<ProcessedFile> _handleHeicConversion(
    String filename,
    Uint8List bytes,
    String type,
  ) async {
    final extension = p.extension(filename).toLowerCase();
    bool isHeic = extension == '.heic';
    bool isHeif = extension == '.heif';

    if (type == 'image' && (isHeic || isHeif)) {
      if (kDebugMode) {
        debugPrint(
          'Attempting to convert HEIC/HEIF: $filename using heif_converter',
        );
      }

      try {
        final tempDir = await getTemporaryDirectory();
        final tempFile = File('${tempDir.path}/${p.basename(filename)}');
        await tempFile.writeAsBytes(bytes);

        if (kDebugMode) {
          debugPrint('HEIC/HEIF saved to temporary path: ${tempFile.path}');
        }

        final outputJpgPath =
            '${tempDir.path}/${p.basenameWithoutExtension(filename)}.jpg';

        final String? resultPath = await HeifConverter.convert(
          tempFile.path,
          output: outputJpgPath,
          format: 'jpeg',
        );

        if (resultPath != null) {
          if (kDebugMode) {
            debugPrint('HEIC/HEIF converted to JPEG successfully: $resultPath');
          }
          final convertedBytes = await File(resultPath).readAsBytes();
          final convertedFilename = p.basename(resultPath);

          return ProcessedFile(
            filename: convertedFilename,
            bytes: convertedBytes,
          );
        } else {
          if (kDebugMode) {
            debugPrint(
              'Failed to convert HEIC/HEIF using heif_converter. Result path is null.',
            );
          }
          throw Exception('HEIC/HEIF conversion failed');
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint(
            'Error during HEIC/HEIF conversion process with heif_converter: $e',
          );
        }
        throw Exception('Error during HEIC/HEIF conversion process: $e');
      }
    }

    return ProcessedFile(filename: filename, bytes: bytes);
  }

  Future<Uint8List> _compressImage(Uint8List bytes, String filename) async {
    try {
      const int maxFileSize = 5 * 1024 * 1024; // 5 MB

      if (bytes.length <= maxFileSize) {
        return bytes;
      }

      img.Image? image = img.decodeImage(bytes);
      if (image == null) {
        logWarning('Failed to decode image: $filename');
        return bytes;
      }

      const int maxWidth = 1920;
      const int maxHeight = 1920;

      if (image.width > maxWidth || image.height > maxHeight) {
        image = img.copyResize(
          image,
          width: image.width > maxWidth ? maxWidth : null,
          height: image.height > maxHeight ? maxHeight : null,
          maintainAspect: true,
        );
      }

      int quality = 85;
      Uint8List compressedBytes;

      do {
        compressedBytes = Uint8List.fromList(
          img.encodeJpg(image, quality: quality),
        );
        quality -= 10;
      } while (compressedBytes.length > maxFileSize && quality > 10);

      logInfo(
        'Image compressed: ${bytes.length} -> ${compressedBytes.length} bytes (quality: ${quality + 10})',
      );
      return compressedBytes;
    } catch (e) {
      logError('Error compressing image: $filename', e);
      return bytes;
    }
  }
}
