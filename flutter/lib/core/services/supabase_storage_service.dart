import 'dart:io';
import 'dart:typed_data';

import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:luxury_app/core/services/file_processing_service.dart';
import 'package:luxury_app/core/services/supabase_service.dart';
import 'package:luxury_app/features/ai_chat/data/ai_attachment.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

/// Сервис для работы с Supabase Storage
/// Заменяет FileUploadService для загрузки файлов в Supabase Storage
class SupabaseStorageService with LoggerMixin {
  final SupabaseService _supabaseService;
  final FileProcessingService _fileProcessingService;
  final Uuid _uuid = const Uuid();

  // Bucket для файлов чатов
  static const String chatAttachmentsBucket = 'chat-attachments';

  SupabaseStorageService({
    required SupabaseService supabaseService,
    required FileProcessingService fileProcessingService,
  }) : _supabaseService = supabaseService,
       _fileProcessingService = fileProcessingService;

  /// Загружает файл в Supabase Storage и возвращает AIAttachmentCreate
  Future<AIAttachmentCreate> uploadFile(
    String filename,
    Uint8List bytes,
    String type,
  ) async {
    try {
      // Обрабатываем файл (сжатие, конвертация)
      final processedFile = await _fileProcessingService.processFile(
        filename,
        bytes,
        type,
      );

      logInfo(
        'Uploading file to Supabase Storage: ${processedFile.filename} '
        '(${processedFile.bytes.length} bytes, type: $type)',
      );

      // Генерируем уникальное имя файла
      final fileExtension = _getFileExtension(processedFile.filename);
      final uniqueFilename = '${_uuid.v4()}$fileExtension';
      final filePath = 'attachments/$uniqueFilename';

      // Загружаем файл в Supabase Storage
      await _supabaseService.client.storage
          .from(chatAttachmentsBucket)
          .uploadBinary(
            filePath,
            processedFile.bytes,
            fileOptions: FileOptions(
              contentType: _getMimeType(type, fileExtension),
              upsert: false,
            ),
          );

      // Получаем публичный URL
      final publicUrl = _supabaseService.client.storage
          .from(chatAttachmentsBucket)
          .getPublicUrl(filePath);

      logInfo('File uploaded successfully: $publicUrl');

      return AIAttachmentCreate(
        type: type,
        url: publicUrl,
        filename: processedFile.filename,
        size: processedFile.bytes.length,
        localBytes: processedFile.bytes, // Сохраняем для превью
        mimeType: _getMimeType(type, fileExtension),
      );
    } catch (e) {
      logError('Error uploading file to Supabase Storage: $filename', e);
      rethrow;
    }
  }

  /// Загружает файл из File объекта
  Future<AIAttachmentCreate> uploadFileFromPath(File file, String type) async {
    final bytes = await file.readAsBytes();
    final filename = file.path.split('/').last;
    return uploadFile(filename, bytes, type);
  }

  /// Удаляет файл из Supabase Storage по URL
  Future<bool> deleteFile(String fileUrl) async {
    try {
      // Извлекаем путь файла из URL
      final filePath = _extractFilePathFromUrl(fileUrl);
      if (filePath == null) {
        logError('Could not extract file path from URL: $fileUrl', null);
        return false;
      }

      await _supabaseService.client.storage.from(chatAttachmentsBucket).remove([
        filePath,
      ]);

      logInfo('File deleted successfully: $filePath');
      return true;
    } catch (e) {
      logError('Error deleting file from Supabase Storage: $fileUrl', e);
      return false;
    }
  }

  /// Получает содержимое файла по URL
  Future<Uint8List?> getFileContent(String fileUrl) async {
    try {
      final filePath = _extractFilePathFromUrl(fileUrl);
      if (filePath == null) {
        logError('Could not extract file path from URL: $fileUrl', null);
        return null;
      }

      final response = await _supabaseService.client.storage
          .from(chatAttachmentsBucket)
          .download(filePath);

      return response;
    } catch (e) {
      logError('Error downloading file from Supabase Storage: $fileUrl', e);
      return null;
    }
  }

  /// Определяет тип файла по расширению
  String determineFileType(String extension) {
    final ext = extension.toLowerCase();
    if ([
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'bmp',
      'tiff',
      'heic',
      'heif',
    ].contains(ext)) {
      return 'image';
    } else if (['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv'].contains(ext)) {
      return 'video';
    } else if (['mp3', 'wav', 'aac', 'flac', 'm4a', 'ogg'].contains(ext)) {
      return 'audio';
    } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].contains(ext)) {
      return 'document';
    } else {
      return 'file';
    }
  }

  /// Извлекает расширение файла
  String _getFileExtension(String filename) {
    final lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex == -1) return '';
    return filename.substring(lastDotIndex);
  }

  /// Определяет MIME тип по типу файла и расширению
  String _getMimeType(String type, String extension) {
    final ext = extension.toLowerCase().replaceFirst('.', '');

    switch (type) {
      case 'image':
        switch (ext) {
          case 'jpg':
          case 'jpeg':
            return 'image/jpeg';
          case 'png':
            return 'image/png';
          case 'gif':
            return 'image/gif';
          case 'webp':
            return 'image/webp';
          case 'bmp':
            return 'image/bmp';
          case 'tiff':
            return 'image/tiff';
          case 'heic':
            return 'image/heic';
          case 'heif':
            return 'image/heif';
          default:
            return 'image/jpeg';
        }
      case 'video':
        switch (ext) {
          case 'mp4':
            return 'video/mp4';
          case 'avi':
            return 'video/x-msvideo';
          case 'mov':
            return 'video/quicktime';
          case 'mkv':
            return 'video/x-matroska';
          case 'webm':
            return 'video/webm';
          default:
            return 'video/mp4';
        }
      case 'audio':
        switch (ext) {
          case 'mp3':
            return 'audio/mpeg';
          case 'wav':
            return 'audio/wav';
          case 'aac':
            return 'audio/aac';
          case 'flac':
            return 'audio/flac';
          case 'm4a':
            return 'audio/mp4';
          case 'ogg':
            return 'audio/ogg';
          default:
            return 'audio/mpeg';
        }
      case 'document':
        switch (ext) {
          case 'pdf':
            return 'application/pdf';
          case 'doc':
            return 'application/msword';
          case 'docx':
            return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          case 'txt':
            return 'text/plain';
          case 'rtf':
            return 'application/rtf';
          default:
            return 'application/octet-stream';
        }
      default:
        return 'application/octet-stream';
    }
  }

  /// Извлекает путь файла из Supabase Storage URL
  String? _extractFilePathFromUrl(String url) {
    try {
      // URL формат: https://project.supabase.co/storage/v1/object/public/bucket/path
      // или локальный: http://127.0.0.1:54321/storage/v1/object/public/bucket/path
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;

      // Ищем сегмент после 'public' и bucket name
      final publicIndex = pathSegments.indexOf('public');
      if (publicIndex == -1 || publicIndex + 2 >= pathSegments.length) {
        return null;
      }

      // Путь файла начинается после bucket name
      final filePathSegments = pathSegments.sublist(publicIndex + 2);
      return filePathSegments.join('/');
    } catch (e) {
      logError('Error parsing Storage URL: $url', e);
      return null;
    }
  }
}
