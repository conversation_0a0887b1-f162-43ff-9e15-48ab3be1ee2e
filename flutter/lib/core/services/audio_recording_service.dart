import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';

class AudioRecordingData {
  final Uint8List bytes;
  final String extension;

  const AudioRecordingData({required this.bytes, required this.extension});
}

class AudioRecordingService with LoggerMixin {
  final AudioRecorder _audioRecorder = AudioRecorder();

  bool _isRecording = false;
  String? _currentFilePath;
  Timer? _recordingTimer;
  Timer? _amplitudeTimer;
  int _recordingSeconds = 0;

  void Function(double amplitude, int seconds)? _amplitudeCallback;

  bool get isRecording => _isRecording;
  int get recordingSeconds => _recordingSeconds;

  Future<bool> startRecording() async {
    try {
      logInfo('Starting audio recording process');

      // Microphone permission service was removed as it's not used
      // We'll rely on the record package's permission handling

      final recordPermission = await _audioRecorder.hasPermission();
      logInfo('Record package permission: $recordPermission');

      if (!recordPermission) {
        logError('No microphone permission from record package');
        return false;
      }

      final directory = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _currentFilePath = '${directory.path}/audio_$timestamp.m4a';
      logInfo('Audio file path: $_currentFilePath');

      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _currentFilePath!,
      );

      logInfo('Audio recording started successfully');

      _isRecording = true;
      _recordingSeconds = 0;

      _startTimers();

      return true;
    } catch (e, s) {
      logError('Error starting audio recording', e, s);
      return false;
    }
  }

  void startAmplitudeMonitoring(
    void Function(double amplitude, int seconds) callback,
  ) {
    _amplitudeCallback = callback;
  }

  void _startTimers() {
    // Таймер для подсчета секунд
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }
      _recordingSeconds++;
      _amplitudeCallback?.call(
        0.1,
        _recordingSeconds,
      ); // Минимальная амплитуда для индикации
    });

    // Таймер для мониторинга амплитуды
    _amplitudeTimer = Timer.periodic(const Duration(milliseconds: 60), (
      timer,
    ) async {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      try {
        final amplitude = await _audioRecorder.getAmplitude();
        double normalizedAmplitude = 0.0;

        if (amplitude.current != double.negativeInfinity &&
            amplitude.current > -80) {
          normalizedAmplitude = ((amplitude.current + 60) / 55).clamp(0.0, 1.0);
          normalizedAmplitude = math.pow(normalizedAmplitude, 0.6).toDouble();
        }

        _amplitudeCallback?.call(normalizedAmplitude, _recordingSeconds);
      } catch (e) {
        _amplitudeCallback?.call(
          0.05,
          _recordingSeconds,
        ); // Минимальное значение при ошибке
      }
    });
  }

  Future<AudioRecordingData?> stopRecording() async {
    _stopTimers();
    _isRecording = false;

    try {
      String? recordedPath = await _audioRecorder.stop();

      if (recordedPath != null) {
        final file = File(recordedPath);
        if (await file.exists()) {
          final bytes = await file.readAsBytes();
          final extension = recordedPath.split('.').last;

          logInfo('Audio recording stopped, file size: ${bytes.length} bytes');

          return AudioRecordingData(bytes: bytes, extension: extension);
        }
      }

      logWarning('No audio file recorded');
      return null;
    } catch (e, s) {
      logError('Error stopping audio recording', e, s);
      return null;
    }
  }

  void _stopTimers() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
    _amplitudeTimer?.cancel();
    _amplitudeTimer = null;
  }

  Future<void> dispose() async {
    try {
      // Останавливаем запись если она активна
      if (_isRecording) {
        await stopRecording();
      }

      // Останавливаем и очищаем таймеры
      _stopTimers();

      // Очищаем callback
      _amplitudeCallback = null;

      // Очищаем путь к файлу
      _currentFilePath = null;

      // Освобождаем ресурсы аудио рекордера
      await _audioRecorder.dispose();

      logInfo('AudioRecordingService disposed successfully');
    } catch (e, s) {
      logError('Error disposing AudioRecordingService', e, s);
    }
  }
}
