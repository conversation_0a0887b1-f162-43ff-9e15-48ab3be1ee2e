import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Сервис для работы с Supabase
class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  /// Клиент Supabase
  SupabaseClient get client => Supabase.instance.client;

  /// Инициализация Supabase
  static Future<void> initialize({
    required String url,
    required String anonKey,
  }) async {
    try {
      await Supabase.initialize(url: url, anonKey: anonKey, debug: kDebugMode);

      if (kDebugMode) {
        log('✅ Supabase инициализирован: $url');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка инициализации Supabase: $e');
      }
      rethrow;
    }
  }

  /// Получение текущего пользователя
  User? get currentUser => client.auth.currentUser;

  /// Получение сессии
  Session? get currentSession => client.auth.currentSession;

  /// Проверка авторизации
  bool get isAuthenticated => currentUser != null;

  /// Получение UUID текущего пользователя
  String? get currentUserId => currentUser?.id;

  /// Регистрация пользователя
  Future<AuthResponse> signUp({
    required String email,
    required String password,
  }) async {
    try {
      if (kDebugMode) {
        log('🚀 Регистрация пользователя: $email');
      }

      final response = await client.auth.signUp(
        email: email,
        password: password,
      );

      if (kDebugMode) {
        if (response.user != null) {
          log('✅ Пользователь зарегистрирован: ${response.user!.email}');
        } else {
          log('⚠️ Регистрация требует подтверждения email');
        }
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка регистрации: $e');
      }
      rethrow;
    }
  }

  /// Вход пользователя
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      if (kDebugMode) {
        log('🚀 Вход пользователя: $email');
      }

      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (kDebugMode) {
        log('✅ Пользователь авторизован: ${response.user?.email}');
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка входа: $e');
      }
      rethrow;
    }
  }

  /// Выход пользователя
  Future<void> signOut() async {
    try {
      if (kDebugMode) {
        log('🚀 Выход пользователя');
      }

      await client.auth.signOut();

      if (kDebugMode) {
        log('✅ Пользователь вышел из системы');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка выхода: $e');
      }
      rethrow;
    }
  }

  /// Сброс пароля
  Future<void> resetPassword({required String email}) async {
    try {
      if (kDebugMode) {
        log('🚀 Сброс пароля для: $email');
      }

      await client.auth.resetPasswordForEmail(email);

      if (kDebugMode) {
        log('✅ Инструкции по сбросу пароля отправлены на: $email');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка сброса пароля: $e');
      }
      rethrow;
    }
  }

  /// Подписка на изменения состояния аутентификации
  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;

  /// Получение таблицы для запросов
  SupabaseQueryBuilder from(String table) => client.from(table);

  /// Получение storage для файлов
  SupabaseStorageClient get storage => client.storage;

  /// Realtime подписка
  RealtimeChannel channel(String name) => client.channel(name);

  /// Освобождение ресурсов
  void dispose() {
    // Supabase client управляется автоматически
  }
}
