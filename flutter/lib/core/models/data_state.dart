import 'package:equatable/equatable.dart';

/// Базовое состояние для данных
class DataState<T> extends Equatable {
  final T? data;
  final bool isLoading;
  final String? error;
  final DataSource source;

  const DataState({
    this.data,
    this.isLoading = false,
    this.error,
    this.source = DataSource.network,
  });

  /// Создает состояние с данными
  DataState<T> copyWithData(T data, {DataSource? source}) {
    return DataState<T>(
      data: data,
      isLoading: false,
      error: null,
      source: source ?? this.source,
    );
  }

  /// Создает состояние загрузки
  DataState<T> copyWithLoading() {
    return DataState<T>(
      data: data,
      isLoading: true,
      error: null,
      source: source,
    );
  }

  /// Создает состояние ошибки
  DataState<T> copyWithError(String error) {
    return DataState<T>(
      data: data,
      isLoading: false,
      error: error,
      source: source,
    );
  }

  @override
  List<Object?> get props => [data, isLoading, error, source];
}

/// Источник данных
enum DataSource { cache, network }
