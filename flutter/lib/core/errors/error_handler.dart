import 'package:flutter/foundation.dart';

class ErrorHandler {
  static void init() {
    if (kDebugMode) {
      FlutterError.onError = (FlutterErrorDetails details) {
        // Игнорируем повторяющиеся layout ошибки
        if (_shouldIgnoreError(details)) return;

        final errorMessage = _formatError(details);
        debugPrint(errorMessage);
      };
    }
  }

  static bool _shouldIgnoreError(FlutterErrorDetails details) {
    final exception = details.exception;

    // Более надежная типизированная проверка ошибок
    if (exception is FlutterError) {
      final message = exception.message;

      // Игнорируем известные безопасные layout ошибки
      if (message.contains('RenderBox was not laid out') ||
          message.contains('BoxConstraints forces an infinite') ||
          message.contains('Cannot hit test a render box with no size')) {
        return true;
      }
    }

    // Игнорируем assertion ошибки в debug режиме для layout
    if (exception is AssertionError) {
      final message = exception.message?.toString() ?? '';
      if (message.contains('_debugDoingThisLayout')) {
        return true;
      }
    }

    return false;
  }

  static String _formatError(FlutterErrorDetails details) {
    final buffer = StringBuffer();
    buffer.writeln('🔴 ${details.exception.runtimeType}');

    final errorStr = details.exception.toString();
    // Показываем только первую строку ошибки
    final firstLine = errorStr.split('\n').first;
    buffer.writeln(firstLine);

    if (details.context != null) {
      buffer.writeln('Widget: ${details.context.runtimeType}');
    }

    return buffer.toString();
  }
}
