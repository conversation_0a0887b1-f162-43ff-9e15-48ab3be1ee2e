import 'package:flutter/foundation.dart';

/// Централизованный нотификатор ошибок для всего приложения
class ErrorNotifier extends ChangeNotifier {
  String? _lastError;

  String? get lastError => _lastError;

  /// Показывает ошибку (вызывает уведомление подписчиков)
  void showError(String message) {
    _lastError = message;
    notifyListeners();
  }

  /// Сброс ошибки (например, после показа SnackBar)
  void clearError() {
    _lastError = null;
    notifyListeners();
  }
}
