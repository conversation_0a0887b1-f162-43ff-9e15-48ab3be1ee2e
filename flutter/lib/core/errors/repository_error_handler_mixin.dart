import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';

mixin RepositoryErrorHandlerMixin on LoggerMixin {
  Future<T> handleApiCall<T>(
    Future<T> Function() call, {
    String? context,
    T? fallbackValue,
    bool logErrors = true,
  }) async {
    try {
      return await call();
    } on DioException catch (e) {
      final errorMessage = _processDioException(e, context);

      if (logErrors) {
        logError('API call failed${context != null ? ' in $context' : ''}', e);
      }

      if (fallbackValue != null) {
        logWarning('Returning fallback value due to API error: $errorMessage');
        return fallbackValue;
      }

      throw Exception(errorMessage);
    } catch (e) {
      final errorMessage = 'Неизвестная ошибка: $e';

      if (logErrors) {
        logError('Unexpected error${context != null ? ' in $context' : ''}', e);
      }

      if (fallbackValue != null) {
        logWarning(
          'Returning fallback value due to unexpected error: $errorMessage',
        );
        return fallbackValue;
      }

      throw Exception(errorMessage);
    }
  }

  String _processDioException(DioException e, String? context) {
    // Определяем тип ошибки и формируем соответствующее сообщение
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Превышено время ожидания соединения. Проверьте подключение к интернету.';
      case DioExceptionType.sendTimeout:
        return 'Превышено время отправки запроса. Попробуйте еще раз.';
      case DioExceptionType.receiveTimeout:
        return 'Превышено время получения ответа. Сервер не отвечает.';
      case DioExceptionType.connectionError:
        return 'Ошибка соединения. Проверьте подключение к интернету.';
      case DioExceptionType.badCertificate:
        return 'Ошибка SSL сертификата. Проверьте безопасность соединения.';
      case DioExceptionType.cancel:
        return 'Запрос был отменен.';
      case DioExceptionType.badResponse:
        return _processBadResponse(e);
      case DioExceptionType.unknown:
        return _processUnknownError(e);
    }
  }

  String _processBadResponse(DioException e) {
    final response = e.response;
    if (response == null) {
      return 'Получен некорректный ответ от сервера.';
    }

    final statusCode = response.statusCode;

    // Обрабатываем стандартные HTTP коды ошибок
    switch (statusCode) {
      case 400:
        return _extractErrorMessage(response.data) ?? 'Некорректный запрос.';
      case 401:
        return 'Необходима авторизация. Войдите в систему.';
      case 403:
        return 'Доступ запрещен. Недостаточно прав.';
      case 404:
        return 'Запрашиваемый ресурс не найден.';
      case 408:
        return 'Превышено время ожидания запроса.';
      case 409:
        return 'Конфликт данных. Попробуйте обновить информацию.';
      case 422:
        return _extractErrorMessage(response.data) ?? 'Некорректные данные.';
      case 429:
        return 'Слишком много запросов. Попробуйте позже.';
      case 500:
        return 'Внутренняя ошибка сервера. Попробуйте позже.';
      case 502:
        return 'Сервер временно недоступен. Попробуйте позже.';
      case 503:
        return 'Сервис временно недоступен. Попробуйте позже.';
      case 504:
        return 'Превышено время ожидания ответа сервера.';
      default:
        return _extractErrorMessage(response.data) ??
            'Ошибка сервера (код: $statusCode).';
    }
  }

  String _processUnknownError(DioException e) {
    final message = e.message;
    if (message != null) {
      if (message.contains('SocketException')) {
        return 'Нет подключения к интернету.';
      }
      if (message.contains('HandshakeException')) {
        return 'Ошибка SSL соединения.';
      }
      if (message.contains('FormatException')) {
        return 'Получен некорректный ответ от сервера.';
      }
    }
    return 'Неизвестная ошибка сети: ${message ?? 'Нет описания'}';
  }

  String? _extractErrorMessage(dynamic data) {
    if (data == null) return null;

    try {
      if (data is Map<String, dynamic>) {
        // Пытаемся извлечь сообщение об ошибке в разных форматах
        final detail = data['detail'];
        final message = data['message'];
        final error = data['error'];
        final errors = data['errors'];

        if (detail != null) {
          return detail.toString();
        }
        if (message != null) {
          return message.toString();
        }
        if (error != null) {
          if (error is Map<String, dynamic>) {
            return error['message']?.toString() ?? error.toString();
          }
          return error.toString();
        }
        if (errors != null && errors is List && errors.isNotEmpty) {
          return errors.first.toString();
        }
      } else if (data is String) {
        return data;
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Ошибка при извлечении сообщения об ошибке: $e');
      }
    }

    return null;
  }
}
