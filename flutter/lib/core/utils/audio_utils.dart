import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart' as ja;
import 'package:luxury_app/shared/constants/api_constants.dart';

/// Возвращает полный URL для аудиофайла, учитывая базовый путь API и убирая параметры кэширования.
String getFullAudioUrl(String url) {
  String cleanUrl = url;
  if (cleanUrl.contains('?nocache=')) {
    cleanUrl = cleanUrl.split('?nocache=')[0];
  }
  return cleanUrl.startsWith('http')
      ? cleanUrl
      : '${ApiConstants.audioFilesUrl}${cleanUrl.startsWith('/') ? cleanUrl : '/$cleanUrl'}';
}

/// Безопасное воспроизведение аудио с обработкой ошибок и логированием.
Future<bool> playAudioWithHandling({
  required ja.AudioPlayer player,
  required String url,
  Duration delay = const Duration(milliseconds: 100),
}) async {
  try {
    String fullUrl = getFullAudioUrl(url);
    await player.stop();
    if (kIsWeb) {
      await player.setAudioSource(
        ja.AudioSource.uri(Uri.parse(fullUrl)),
        preload: true,
      );
    } else {
      await player.setUrl(fullUrl);
    }
    await Future.delayed(delay);
    return true;
  } catch (e) {
    try {
      await player.stop();
    } catch (_) {}
    if (kDebugMode) {
      debugPrint('Ошибка воспроизведения аудио: $e');
    }
    return false;
  }
}
