import 'package:logger/logger.dart';

/// Миксин для упрощенного логирования
mixin LoggerMixin {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      lineLength: 120,
      colors: true,
      printEmojis: false,
    ),
  );

  /// Логирование информации
  void logInfo(String message) => _logger.i(message);

  /// Логирование предупреждений
  void logWarning(String message, [dynamic error]) => _logger.w(message, error: error);

  /// Логирование ошибок
  void logError(String message, [dynamic error, StackTrace? stackTrace]) => 
      _logger.e(message, error: error, stackTrace: stackTrace);

  /// Логирование отладочной информации (только в debug режиме)
  void logDebug(String message) {
    assert(() {
      _logger.d(message);
      return true;
    }());
  }
} 