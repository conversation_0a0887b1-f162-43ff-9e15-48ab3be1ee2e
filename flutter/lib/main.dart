import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app.dart';
import 'package:luxury_app/app_initializer.dart';
import 'package:luxury_app/core/errors/error_handler.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Загружаем переменные окружения
  await dotenv.load(fileName: ".env");

  // Инициализируем обработчик ошибок
  ErrorHandler.init();

  // Отключаем лишние логи в релизном режиме
  if (kReleaseMode) {
    debugPrint = (String? message, {int? wrapWidth}) {};
  }

  // Скрываем системные логи
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(statusBarColor: Colors.transparent),
  );

  await AppInitializer.initialize();

  runApp(const ProviderScope(child: App()));
}
