import 'package:equatable/equatable.dart';

/// Режим отображения drawer
enum DrawerMode { chat, wiki, news }

/// Состояние приложения
class AppState extends Equatable {
  /// Активный экран
  final DrawerMode activeScreen;

  /// Режим drawer
  final DrawerMode drawerMode;

  /// Ширина drawer
  final double drawerWidth;

  /// Последний запрошенный ID страницы
  final String? lastRequestedPageId;

  /// Последний запрошенный ID ассистента
  final String? lastRequestedAssistantId;

  /// Заголовок текущего экрана для AppBar
  final String? currentScreenTitle;

  const AppState({
    this.activeScreen = DrawerMode.chat,
    this.drawerMode = DrawerMode.chat,
    this.drawerWidth = 340.0,
    this.lastRequestedPageId,
    this.lastRequestedAssistantId,
    this.currentScreenTitle,
  });

  /// Создает копию состояния с новыми значениями
  AppState copyWith({
    DrawerMode? activeScreen,
    DrawerMode? drawerMode,
    double? drawerWidth,
    String? lastRequestedPageId,
    String? lastRequestedAssistantId,
    String? currentScreenTitle,
  }) {
    return AppState(
      activeScreen: activeScreen ?? this.activeScreen,
      drawerMode: drawerMode ?? this.drawerMode,
      drawerWidth: drawerWidth ?? this.drawerWidth,
      lastRequestedPageId: lastRequestedPageId ?? this.lastRequestedPageId,
      lastRequestedAssistantId:
          lastRequestedAssistantId ?? this.lastRequestedAssistantId,
      currentScreenTitle: currentScreenTitle ?? this.currentScreenTitle,
    );
  }

  @override
  List<Object?> get props => [
    activeScreen,
    drawerMode,
    drawerWidth,
    lastRequestedPageId,
    lastRequestedAssistantId,
    currentScreenTitle,
  ];
}
