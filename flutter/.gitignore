# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
.packages

# Allow web build output, ignore others
build/*
!build/web/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Platform-specific build files
**/android/**/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew*
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.*

# iOS/macOS
**/ios/**/*.mode*v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/DerivedData/
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/xcuserdata
**/ios/Flutter/
**/macos/Flutter/
**/macos/build/

# Build directories
**/build/*
!build/web/**

# Large build files
**/*.dill
**/kernel_blob.bin
**/FlutterMacOS.framework/Versions/A/FlutterMacOS

# windsurf rules
.windsurfrules
