---
description: 
globs: 
alwaysApply: true
---
Luxury App - проект для сотрудников "Luxury Sound", включает в себя Wiki систему, AI-ассистента и корпоративные инструменты.

Проект включает фронтенд на Flutter и Supabase как источник синхронизации данных между пользователями и устройствами пользователей.

Luxury App - приложение lockal first, client first, realtime. Вся логика и данные хранятся на клиенте и используют умную реалтайм бэкграунд синхронизацию с удалленой базой данных supabase, так чтобы пользователь мог беспроблемно работать на разных устройствах и всегда иметь доступ ко всем своим данным как онлайн так и офлайн.

## Основные функции

- **AI-ассистент**: Чат с историей, загрузкой файлов и изображений, транскрибация аудио (новая модель gpt-4o-mini-transcribe), инструменты.
- **База знаний (Wiki)**: Иерархия папок и статей, Markdown, поиск, кэширование, аудио-озвучка.
- **Новости**: Лента новостей с форматированием и сортировкой.
- **Пользователи**: через Supabase