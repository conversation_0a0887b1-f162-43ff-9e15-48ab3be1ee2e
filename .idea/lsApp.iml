<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/flutter/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/app_links/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/app_links/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/app_links/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/app_links/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/app_links/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/app_links/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/file_picker/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/file_picker/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/file_picker/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/file_picker/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/file_picker/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/file_picker/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/just_audio/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/just_audio/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/just_audio/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/just_audio/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/just_audio/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/just_audio/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/record_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/record_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/record_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/share_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/share_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/share_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/share_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/share_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter/ios/.symlinks/plugins/share_plus/example/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>