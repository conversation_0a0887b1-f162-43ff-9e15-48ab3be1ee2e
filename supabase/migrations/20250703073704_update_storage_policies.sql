-- Update Storage RLS policies for chat attachments
-- Files are stored as: attachments/{uuid}.{extension}

-- Drop existing policies
drop policy if exists "Users can upload their own chat attachments" on storage.objects;
drop policy if exists "Users can view their own chat attachments" on storage.objects;

-- Create new policies for authenticated users to manage chat attachments
create policy "Authenticated users can upload chat attachments" on storage.objects
    for insert with check (
        bucket_id = 'chat-attachments' and
        auth.role() = 'authenticated' and
        (storage.foldername(name))[1] = 'attachments'
    );

create policy "Authenticated users can view chat attachments" on storage.objects
    for select using (
        bucket_id = 'chat-attachments' and
        auth.role() = 'authenticated' and
        (storage.foldername(name))[1] = 'attachments'
    );

create policy "Authenticated users can delete their own chat attachments" on storage.objects
    for delete using (
        bucket_id = 'chat-attachments' and
        auth.role() = 'authenticated' and
        (storage.foldername(name))[1] = 'attachments'
    );

-- Ensure bucket is public for reading (but controlled by RLS)
update storage.buckets 
set public = true 
where id = 'chat-attachments';

-- Ensure wiki-audio bucket policies
create policy "Anyone can upload wiki audio files" on storage.objects
    for insert with check (
        bucket_id = 'wiki-audio' and
        (storage.foldername(name))[1] = 'audio'
    );

create policy "Anyone can delete wiki audio files" on storage.objects
    for delete using (
        bucket_id = 'wiki-audio' and
        (storage.foldername(name))[1] = 'audio'
    );
