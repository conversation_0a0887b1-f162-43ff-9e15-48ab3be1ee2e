-- Initial schema migration for LuxuryApp
-- Based on FastAPI SQLAlchemy models, adapted for Supabase

-- Enable necessary extensions
create extension if not exists "uuid-ossp";

-- Create custom types
create type chat_message_role as enum ('user', 'assistant', 'system');
create type attachment_type as enum ('image', 'document', 'audio', 'video', 'other');

-- ============================================================================
-- CHATS TABLES (require RLS by user_id)
-- ============================================================================

-- Chats table (связывается с auth.users через user_id)
create table chats (
    id bigserial primary key,
    title text not null,
    user_id uuid not null references auth.users(id) on delete cascade,
    created_at timestamptz default now() not null,
    updated_at timestamptz default now() not null
);

-- Messages table 
create table messages (
    id bigserial primary key,
    chat_id bigint not null references chats(id) on delete cascade,
    role chat_message_role not null,
    content text,
    metadata jsonb,
    created_at timestamptz default now() not null
);

-- Attachments table
create table attachments (
    id bigserial primary key,
    message_id bigint not null references messages(id) on delete cascade,
    type attachment_type not null,
    url text not null,
    filename text,
    mime_type text,
    size integer,
    width integer,
    height integer,
    metadata jsonb,
    created_at timestamptz default now() not null
);

-- ============================================================================
-- WIKI TABLES (public read access)
-- ============================================================================

-- Wiki folders table
create table wiki_folders (
    id uuid primary key default uuid_generate_v4(),
    name text not null,
    parent_id uuid references wiki_folders(id) on delete cascade,
    created_at timestamptz default now() not null,
    icon_0 text,
    icon_1 text,
    sort_order integer not null default 0
);

-- Wiki files table
create table wiki_files (
    id uuid primary key default uuid_generate_v4(),
    name text not null,
    markdown text not null,
    folder_id uuid not null references wiki_folders(id) on delete cascade,
    created_at timestamptz default now() not null,
    sort_order integer not null default 0,
    text text,
    audio_url text,
    words text
);

-- ============================================================================
-- NEWS TABLE (public read access)
-- ============================================================================

-- News table
create table news (
    id bigserial primary key,
    content text not null,
    created_at timestamptz default now() not null
);

-- ============================================================================
-- INDEXES for performance
-- ============================================================================

-- Chats indexes
create index idx_chats_user_id on chats(user_id);
create index idx_chats_created_at on chats(created_at desc);

-- Messages indexes  
create index idx_messages_chat_id on messages(chat_id);
create index idx_messages_created_at on messages(created_at desc);

-- Attachments indexes
create index idx_attachments_message_id on attachments(message_id);

-- Wiki indexes
create index idx_wiki_folders_parent_id on wiki_folders(parent_id);
create index idx_wiki_folders_sort_order on wiki_folders(sort_order);
create index idx_wiki_files_folder_id on wiki_files(folder_id);
create index idx_wiki_files_sort_order on wiki_files(sort_order);

-- News indexes
create index idx_news_created_at on news(created_at desc);

-- ============================================================================
-- RLS POLICIES
-- ============================================================================

-- Enable RLS on all tables
alter table chats enable row level security;
alter table messages enable row level security;
alter table attachments enable row level security;
alter table wiki_folders enable row level security;
alter table wiki_files enable row level security;
alter table news enable row level security;

-- CHATS: Users can only access their own chats
create policy "Users can only see their own chats" on chats
    for all using (auth.uid() = user_id);

-- MESSAGES: Users can only access messages from their chats
create policy "Users can only see messages from their chats" on messages
    for all using (
        chat_id in (
            select id from chats where user_id = auth.uid()
        )
    );

-- ATTACHMENTS: Users can only access attachments from their messages
create policy "Users can only see attachments from their messages" on attachments
    for all using (
        message_id in (
            select m.id from messages m
            join chats c on m.chat_id = c.id 
            where c.user_id = auth.uid()
        )
    );

-- WIKI: Public read access for all users
create policy "Wiki folders are publicly readable" on wiki_folders
    for select using (true);

create policy "Wiki files are publicly readable" on wiki_files  
    for select using (true);

-- Для админов - возможность редактировать Wiki (пока отключено)
-- create policy "Admins can manage wiki folders" on wiki_folders
--     for all using (
--         auth.jwt() ->> 'role' = 'admin'
--     );

-- NEWS: Public read access for all users
create policy "News are publicly readable" on news
    for select using (true);

-- ============================================================================
-- REALTIME SUBSCRIPTIONS
-- ============================================================================

-- Enable realtime for tables that need live updates
alter publication supabase_realtime add table chats;
alter publication supabase_realtime add table messages;
alter publication supabase_realtime add table attachments;
alter publication supabase_realtime add table wiki_folders;
alter publication supabase_realtime add table wiki_files;
alter publication supabase_realtime add table news;

-- ============================================================================
-- TRIGGERS for updated_at
-- ============================================================================

-- Function to update updated_at timestamp
create or replace function update_updated_at_column()
returns trigger as $$
begin
    new.updated_at = now();
    return new;
end;
$$ language plpgsql;

-- Trigger for chats table
create trigger update_chats_updated_at 
    before update on chats
    for each row execute procedure update_updated_at_column();

-- ============================================================================
-- STORAGE BUCKETS (for file uploads)
-- ============================================================================

-- Create storage bucket for chat attachments
insert into storage.buckets (id, name, public) 
values ('chat-attachments', 'chat-attachments', false);

-- Create storage bucket for wiki audio files
insert into storage.buckets (id, name, public)
values ('wiki-audio', 'wiki-audio', true);

-- RLS policies for storage
create policy "Users can upload their own chat attachments" on storage.objects
    for insert with check (
        bucket_id = 'chat-attachments' and
        auth.uid()::text = (storage.foldername(name))[1]
    );

create policy "Users can view their own chat attachments" on storage.objects
    for select using (
        bucket_id = 'chat-attachments' and
        auth.uid()::text = (storage.foldername(name))[1]
    );

create policy "Anyone can view wiki audio files" on storage.objects
    for select using (bucket_id = 'wiki-audio'); 