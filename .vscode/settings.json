{"[dart]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}, "[json]": {"editor.formatOnSave": true}, "[python]": {"editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "editor.formatOnSave": true}, "[typescript]": {"editor.defaultFormatter": "denoland.vscode-deno"}, "[yaml]": {"editor.formatOnSave": true}, "chat.experimental.implicitContext": true, "containers.contexts.label": "Description", "dart.lineLength": 80, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "docker.contexts.label": "Description", "docker.contexts.showInStatusBar": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.formatOnType": true, "python.analysis.autoImportCompletions": true, "python.analysis.autoSearchPaths": true, "python.analysis.diagnosticMode": "workspace", "python.analysis.exclude": ["**/venv/**", "**/node_modules/**"], "python.analysis.include": ["fastapi/**"], "python.analysis.typeCheckingMode": "basic", "python.createEnvironment.contentButton": "show", "python.languageServer": "None", "python.linting.enabled": true, "python.linting.flake8Enabled": false, "python.linting.lintOnSave": true, "python.linting.pylintArgs": ["--load-plugins=pylint_django", "--django-settings-module=settings", "--disable=C0111,C0103,R0903,R0902,W0613,W0622"], "python.linting.pylintEnabled": true}