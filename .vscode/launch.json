{
    "version": "0.2.0",
    "inputs": [
        {
            "id": "flutterDevice",
            "type": "pickString",
            "description": "Выберите устройство для запуска Flutter",
            "options": [
                "macos",
                "chrome",
                "00008110-000575DC3E03801E" // iPhone (Константин) (wireless)
            ],
            "default": "macos"
        }
    ],
    "compounds": [
        {
            "name": "Debug App (Backend + Frontend)",
            "configurations": [
                "Start FastAPI Backend",
                "Run Flutter on selected device"
            ],
            "stopAll": true
        }
    ],
    "configurations": [
        {
            "name": "Start FastAPI Backend",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/fastapi/main.py",
            "cwd": "${workspaceFolder}/fastapi",
            "console": "integratedTerminal",
            "python": "${workspaceFolder}/fastapi/venv/bin/python"
        },
        {
            "name": "Run Flutter on selected device",
            "cwd": "flutter",
            "request": "launch",
            "type": "dart",
            "deviceId": "${input:flutterDevice}"
        },
        {
            "name": "Flutter (profile mode)",
            "cwd": "flutter",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "Flutter (release mode)",
            "cwd": "flutter",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}